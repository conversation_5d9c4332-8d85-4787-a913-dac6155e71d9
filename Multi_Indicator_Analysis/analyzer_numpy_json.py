#!/usr/bin/env python3
"""
期货技术分析系统 v0.6 - NumPy版本 (JSON输出)
基于NumPy/SciPy自主实现的独立分析脚本，输出JSON格式报告
"""

import sys
import os
import argparse
import logging
import re
import json
import uuid
from datetime import datetime
from urllib.parse import urlparse

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from remote_data_loader import RemoteDataLoader
from numpy_technical_indicators import NumpyTechnicalIndicators
from enhanced_analysis_engine import EnhancedAnalysisEngine
from json_report_generator import JsonReportGenerator

# 配置日志
# 获取脚本所在目录，确保日志文件保存在脚本目录下
# script_dir = os.path.dirname(os.path.abspath(__file__))
# log_file_path = os.path.join(script_dir, 'analysis_numpy_json.log')

# logging.basicConfig(
#     level=logging.ERROR,
#     format='%(asctime)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler(log_file_path)
#     ]
# )

def parse_url_contract(url_str):
    """解析URL格式的合约参数"""
    # 格式: http://*************:9090/market-data/CFFEX/IF2509_Day.txt#uhjhggjj#3001
    try:
        # 分离URL和参数
        if '#' in url_str:
            url_part, req_id, function_code = url_str.split('#', 2)
        else:
            raise argparse.ArgumentTypeError("URL格式错误，缺少req_id和功能编号")
        
        # 解析URL
        parsed_url = urlparse(url_part)
        if not parsed_url.scheme or not parsed_url.netloc:
            raise argparse.ArgumentTypeError("无效的URL格式")
        
        # 提取路径信息
        path_parts = parsed_url.path.strip('/').split('/')
        if len(path_parts) < 3:
            raise argparse.ArgumentTypeError("URL路径格式错误，应为: market-data/交易所/合约文件")
        
        # 验证路径格式
        if path_parts[0] != 'market-data':
            raise argparse.ArgumentTypeError("URL路径必须以'market-data'开头")
        
        exchange = path_parts[1]
        contract_file = path_parts[2]
        
        # 验证交易所
        valid_exchanges = ['CFFEX', 'SHFE', 'DCE', 'ZCE', 'GFEX']
        if exchange not in valid_exchanges:
            raise argparse.ArgumentTypeError(
                f"不支持的交易所: {exchange}. 支持的交易所: {', '.join(valid_exchanges)}"
            )
        
        # 从文件名提取合约代码
        # 格式: IF2509_Day.txt 或 IF2509_Minute_15.txt
        contract_match = re.match(r'([A-Za-z0-9]+)_(Day|Minute_15)\.txt', contract_file)
        if not contract_match:
            raise argparse.ArgumentTypeError(f"合约文件名格式错误: {contract_file}")
        
        contract_code = contract_match.group(1)
        data_type = contract_match.group(2)
        
        # 验证功能编号
        if function_code != '3001':
            raise argparse.ArgumentTypeError(f"不支持的功能编号: {function_code}，当前仅支持: 3001")
        
        return {
            'base_url': f"{parsed_url.scheme}://{parsed_url.netloc}/market-data",
            'exchange': exchange,
            'contract': contract_code,
            'req_id': req_id,
            'function_code': function_code,
            'data_type': data_type
        }
        
    except ValueError as e:
        raise argparse.ArgumentTypeError(f"URL参数解析失败: {str(e)}")
    except Exception as e:
        raise argparse.ArgumentTypeError(f"URL格式错误: {str(e)}")

def validate_contract_format(contract_str):
    """验证合约格式"""
    if contract_str.startswith('http'):
        return parse_url_contract(contract_str)
    
    # 原有的合约格式验证逻辑
    if '/' not in contract_str:
        raise argparse.ArgumentTypeError(
            f"合约格式错误: {contract_str}. 正确格式: 交易所/合约代码 (如: CFFEX/IF2509)"
        )
    
    exchange, contract = contract_str.split('/', 1)
    
    # 验证交易所
    valid_exchanges = ['CFFEX', 'SHFE', 'DCE', 'ZCE', 'GFEX']
    if exchange not in valid_exchanges:
        raise argparse.ArgumentTypeError(
            f"不支持的交易所: {exchange}. 支持的交易所: {', '.join(valid_exchanges)}"
        )
    
    return {
        'exchange': exchange,
        'contract': contract,
        'req_id': None,
        'function_code': None
    }

def check_data_availability(exchange, contract, loader):
    """检查数据可用性"""
    try:
        # 检查网络连接 - 使用实际的交易所和合约进行测试
        if not loader.test_connection(exchange, contract):
            return False, f"无法连接到数据服务器或访问 {exchange}/{contract} 数据，请检查网络连接和数据文件是否存在"
        
        # 检查交易所是否支持
        available_exchanges = loader.list_available_exchanges()
        if exchange not in available_exchanges:
            return False, f"交易所 {exchange} 不在支持列表中，支持的交易所: {', '.join(available_exchanges)}"
        
        # 检查必需的数据周期是否支持
        periods = loader.get_available_periods(exchange, contract)
        required_periods = ['15分钟线', '日线']
        missing_periods = [p for p in required_periods if p not in periods]
        
        if missing_periods:
            return False, f"缺少必需的数据周期: {', '.join(missing_periods)}"
        
        return True, ""
        
    except Exception as e:
        return False, f"数据检查失败: {str(e)}"

def load_and_validate_data(loader, exchange, contract, base_url=None):
    """加载和验证数据"""
    print("📊 加载数据...")
    
    # 加载15分钟线数据
    print(f"  🔍 尝试加载15分钟线数据: {exchange}/{contract}")
    minute_data = loader.load_contract_data(exchange, contract, '15分钟线')
    if minute_data is None:
        raise ValueError(f"无法获取15分钟线数据，请检查文件是否存在: {exchange}/{contract}_Minute_15.txt")
    
    minute_count = len(minute_data) if minute_data is not None else 0
    print(f"  📊 15分钟线数据条数: {minute_count}")
    
    if minute_count < 100:
        raise ValueError(f"15分钟线数据不足，当前只有{minute_count}条记录，需要至少100条记录")
    
    # 加载日线数据
    print(f"  🔍 尝试加载日线数据: {exchange}/{contract}")
    daily_data = loader.load_contract_data(exchange, contract, '日线')
    if daily_data is None:
        raise ValueError(f"无法获取日线数据，请检查文件是否存在: {exchange}/{contract}_Day.txt")
    
    daily_count = len(daily_data) if daily_data is not None else 0
    print(f"  📊 日线数据条数: {daily_count}")
    
    if daily_count < 60:
        raise ValueError(f"日线数据不足，当前只有{daily_count}条记录，需要至少60条记录")
    
    print(f"  ✅ 15分钟线数据: {minute_count} 条记录")
    print(f"  ✅ 日线数据: {daily_count} 条记录")
    
    return minute_data, daily_data

def calculate_indicators(calculator, minute_data, daily_data):
    """计算技术指标"""
    print("🔧 计算技术指标 (NumPy/SciPy)...")
    
    # 计算15分钟线指标
    minute_indicators = calculator.calculate_all_indicators(minute_data)
    if minute_indicators is None:
        raise ValueError("15分钟线指标计算失败")
    print("  ✅ 15分钟线指标计算完成")
    
    # 计算日线指标
    daily_indicators = calculator.calculate_all_indicators(daily_data)
    if daily_indicators is None:
        raise ValueError("日线指标计算失败")
    print("  ✅ 日线指标计算完成")
    
    return minute_indicators, daily_indicators

def generate_analysis(engine, minute_indicators, daily_indicators, minute_data, daily_data, exchange, contract):
    """生成综合分析"""
    print("📝 生成综合分析...")

    analysis = engine.generate_comprehensive_analysis(
        exchange, contract, minute_data, daily_data,
        minute_indicators, daily_indicators
    )

    if analysis is None:
        raise ValueError("分析生成失败")

    print("  ✅ 综合分析生成完成")
    return analysis

def save_json_report(report_generator, analysis, req_id=None):
    """保存JSON格式分析报告"""
    # print("💾 保存JSON分析报告...")

    report_path = report_generator.save_json_report(analysis, req_id)
    if report_path is None:
        raise ValueError("JSON报告生成失败")

    # print(f"  ✅ JSON报告已保存: {report_path}")
    return report_path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='期货技术分析系统 v0.6 - NumPy版本 (JSON输出)',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  URL格式 (推荐):
    python analyzer_numpy_json.py "http://*************:9090/market-data/CFFEX/IF2509_Day.txt#uhjhggjj#3001"
    python analyzer_numpy_json.py "http://*************:9090/market-data/SHFE/au2510_Day.txt#req123#3001" --verbose

URL格式说明:
  格式: http://服务器地址:端口/market-data/交易所/合约文件#req_id#功能编号
  示例: http://*************:9090/market-data/CFFEX/IF2509_Day.txt#uhjhggjj#3001
  - 合约文件格式: 合约代码_Day.txt 或 合约代码_Minute_15.txt
  - req_id: 请求标识符
  - 功能编号: 当前仅支持 3001
        """
    )
    
    parser.add_argument(
        'contract',
        type=validate_contract_format,
        help='合约代码，支持两种格式:\n1. 交易所/合约代码 (如: CFFEX/IF2509)\n2. URL格式 (如: http://*************:9090/market-data/CFFEX/IF2509_Day.txt#req_id#3001)'
    )
    
    parser.add_argument(
        '--req-id',
        help='请求ID，用于填充JSON报告中的req_id字段'
    )
    
    # 获取脚本所在目录，确保输出目录在脚本目录下
    script_dir = os.path.dirname(os.path.abspath(__file__))
    default_output_dir = os.path.join(script_dir, 'analysis_results')
    
    parser.add_argument(
        '--output-dir',
        default=default_output_dir,
        help=f'输出目录 (默认: {default_output_dir})'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细输出'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.INFO)
    
    print("🚀 期货技术分析系统 v0.6 (NumPy版本 - JSON输出)")
    print("🔬 基于NumPy/SciPy自主实现，输出JSON格式报告")
    print("="*60)
    
    try:
        # 解析合约参数
        contract_info = args.contract
        
        # 处理URL格式
        if isinstance(contract_info, dict):
            exchange = contract_info['exchange']
            contract = contract_info['contract']
            req_id = contract_info['req_id']
            function_code = contract_info['function_code']
            base_url = contract_info.get('base_url')
            
            print(f"📈 分析合约 (URL格式): {args.contract}")
            print(f"   基础URL: {base_url}")
            print(f"   交易所: {exchange}")
            print(f"   合约代码: {contract}")
            print(f"   请求ID: {req_id}")
            print(f"   功能编号: {function_code}")
            
            # 验证功能编号
            if function_code != '3001':
                error_msg = f"不支持的功能编号: {function_code}"
                print(f"❌ {error_msg}")
                # 生成错误JSON报告
                report_generator = JsonReportGenerator(args.output_dir)
                error_report_path = report_generator.save_error_json_report(exchange, contract, error_msg, req_id)
                print(f"📁 错误报告文件: {error_report_path}")
                sys.exit(1)
        else:
            # 处理传统格式
            exchange, contract = args.contract.split('/')
            req_id = args.req_id
            function_code = None
            base_url = None  # 传统格式使用默认base_url
            
            print(f"📈 分析合约: {args.contract}")
            print(f"   交易所: {exchange}")
            print(f"   合约代码: {contract}")
            if req_id:
                print(f"   请求ID: {req_id}")
        
        # 初始化组件
        # print("🔗 初始化远程数据连接...")
        if base_url:
            # 使用指定的URL
            loader = RemoteDataLoader(base_url=base_url)
            # print(f"   使用指定服务器: {base_url}")
        else:
            # 使用默认配置
            loader = RemoteDataLoader()
        
        # print("🔧 初始化NumPy技术指标计算器...")
        calculator = NumpyTechnicalIndicators()
        
        engine = EnhancedAnalysisEngine()
        report_generator = JsonReportGenerator(args.output_dir)
        
        # 检查数据可用性
        is_available, error_msg = check_data_availability(exchange, contract, loader)
        if not is_available:
            print(f"❌ {error_msg}")
            # 生成错误JSON报告
            error_report_path = report_generator.save_error_json_report(exchange, contract, error_msg, req_id)
            print(f"📁 错误报告文件: {error_report_path}")
            sys.exit(1)
        
        # 加载数据
        minute_data, daily_data = load_and_validate_data(loader, exchange, contract, base_url)
        
        # 计算指标
        minute_indicators, daily_indicators = calculate_indicators(
            calculator, minute_data, daily_data
        )
        
        # 生成分析
        analysis = generate_analysis(
            engine, minute_indicators, daily_indicators,
            minute_data, daily_data, exchange, contract
        )
        
        # 获取数据加载时的request ID（如果有的话）
        data_request_id = loader.get_last_request_id()
        final_req_id = req_id or data_request_id

        # 保存JSON报告
        report_path = save_json_report(report_generator, analysis, final_req_id)
        
        print(f"\n✅ 分析完成！")
        print(f"📁 JSON报告文件: {report_path}")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断操作")
        # 生成错误JSON报告
        if 'exchange' in locals() and 'contract' in locals() and 'report_generator' in locals():
            error_report_path = report_generator.save_error_json_report(exchange, contract, "用户中断操作", req_id)
            print(f"📁 错误报告文件: {error_report_path}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 分析失败: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        # 生成错误JSON报告
        if 'exchange' in locals() and 'contract' in locals() and 'report_generator' in locals():
            error_report_path = report_generator.save_error_json_report(exchange, contract, str(e), req_id)
            print(f"📁 错误报告文件: {error_report_path}")
        sys.exit(1)

if __name__ == "__main__":
    main()
