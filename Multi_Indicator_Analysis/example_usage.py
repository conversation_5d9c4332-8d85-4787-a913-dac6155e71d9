#!/usr/bin/env python3
"""
NumPy最小版本使用示例
"""

import subprocess
import sys
import os
import json

def run_analysis_example():
    """运行分析示例"""
    print("🚀 NumPy最小版本使用示例")
    print("="*50)
    
    # 示例合约列表
    examples = [
        ("CFFEX/IF2509", "中金所股指期货"),
        ("SHFE/au2510", "上期所黄金期货"),
        ("DCE/a2509", "大商所豆粕期货")
    ]
    
    print("📊 可用的分析示例:")
    for i, (contract, desc) in enumerate(examples, 1):
        print(f"  {i}. {contract} - {desc}")
    
    # 让用户选择
    try:
        choice = input("\n请选择要分析的合约 (1-3, 或直接输入合约代码): ").strip()
        
        if choice in ['1', '2', '3']:
            contract = examples[int(choice) - 1][0]
        elif '/' in choice:
            contract = choice
        else:
            print("❌ 输入格式错误")
            return
        
        print(f"\n📈 开始分析合约: {contract}")
        
        # 构建命令
        cmd = [
            sys.executable, 
            "analyzer_numpy_json.py", 
            contract,
            "--req-id", f"example-{contract.replace('/', '-')}",
            "--verbose"
        ]
        
        print(f"🔧 执行命令: {' '.join(cmd)}")
        
        # 执行分析
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 分析完成!")
            print("\n📄 输出:")
            print(result.stdout)
            
            # 查找生成的JSON文件
            if os.path.exists("analysis_results"):
                json_files = [f for f in os.listdir("analysis_results") if f.endswith('.json')]
                if json_files:
                    latest_file = max(json_files, key=lambda x: os.path.getctime(os.path.join("analysis_results", x)))
                    print(f"\n📁 生成的JSON文件: analysis_results/{latest_file}")
                    
                    # 显示JSON内容摘要
                    show_json_summary(f"analysis_results/{latest_file}")
        else:
            print("❌ 分析失败!")
            print(f"错误信息: {result.stderr}")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户取消操作")
    except Exception as e:
        print(f"❌ 执行失败: {e}")

def show_json_summary(json_file):
    """显示JSON文件摘要"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("\n📊 分析结果摘要:")
        print("-" * 30)
        
        analysis_data = data.get('data', {})
        
        # 基本信息
        print(f"合约代码: {analysis_data.get('contract_code')}")
        print(f"分析时间: {analysis_data.get('analysis_time')}")
        
        # 趋势分析
        trend = analysis_data.get('trend_analysis', {}).get('overall_trend', {})
        print(f"趋势方向: {trend.get('direction')}")
        print(f"趋势强度: {trend.get('strength')}")
        
        # 投资建议
        summary = analysis_data.get('summary', {})
        print(f"投资建议: {summary.get('comprehensive_result')}")
        
        # 关键信号
        key_signals = summary.get('key_signals', [])
        if key_signals:
            print(f"关键信号: {key_signals[0]}")
        
        print(f"\n💡 完整分析结果请查看: {json_file}")
        
    except Exception as e:
        print(f"❌ 读取JSON文件失败: {e}")

def show_help():
    """显示帮助信息"""
    print("\n📖 使用帮助")
    print("="*50)
    print("1. 基本分析:")
    print("   python analyzer_numpy_json.py CFFEX/IF2509")
    print()
    print("2. 指定请求ID:")
    print("   python analyzer_numpy_json.py CFFEX/IF2509 --req-id 'my-request-123'")
    print()
    print("3. 指定输出目录:")
    print("   python analyzer_numpy_json.py SHFE/au2510 --output-dir my_reports")
    print()
    print("4. 详细输出模式:")
    print("   python analyzer_numpy_json.py DCE/a2509 --verbose")
    print()
    print("5. 查看所有选项:")
    print("   python analyzer_numpy_json.py --help")

def main():
    """主函数"""
    print("🎯 NumPy最小版本使用示例")
    print("="*50)
    
    while True:
        print("\n请选择操作:")
        print("1. 运行分析示例")
        print("2. 查看使用帮助")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            run_analysis_example()
        elif choice == '2':
            show_help()
        elif choice == '3':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
