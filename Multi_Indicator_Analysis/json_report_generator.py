"""
JSON格式报告生成器 v0.6
基于numpy版本的计算实现，输出JSON格式的分析报告
"""

import os
import json
import uuid
from datetime import datetime

class JsonReportGenerator:
    """JSON格式报告生成器"""
    
    def __init__(self, output_dir="analysis_results"):
        """初始化JSON报告生成器"""
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def save_json_report(self, analysis, req_id=None, filename=None):
        """保存JSON格式的分析报告"""
        if filename is None:
            # 使用简化的文件名格式：交易所_合约代码.json
            filename = f"{analysis['exchange']}_{analysis['contract']}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        # 构建JSON报告结构
        json_report = {
            "func_id": "3001",
            "req_id": req_id or str(uuid.uuid4()),
            "error_id": "0",  # 成功输出
            "data": self._build_analysis_data(analysis)
        }
        
        # 保存JSON文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_report, f, ensure_ascii=False, indent=2)
        
        return filepath
    
    def save_error_json_report(self, exchange, contract, error_msg, req_id=None, filename=None):
        """保存错误JSON报告"""
        if filename is None:
            # 使用与成功情况相同的文件名格式：交易所_合约代码.json
            filename = f"{exchange}_{contract}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        # 构建错误JSON报告结构
        error_report = {
            "func_id": "3001",
            "req_id": req_id or str(uuid.uuid4()),
            "error_id": "1",  # 错误状态
            "data": {
                "contract_code": f"{exchange}/{contract}",
                "analysis_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "error_message": error_msg,
                "status": "分析失败"
            }
        }
        
        # 保存错误JSON文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(error_report, f, ensure_ascii=False, indent=2)
        
        return filepath
    
    def _build_analysis_data(self, analysis):
        """构建分析数据结构"""
        return {
            "contract_code": analysis['contract'],
            "analysis_time": analysis['timestamp'].strftime('%Y-%m-%d %H:%M:%S'),
            "trend_analysis": self._build_trend_analysis(analysis['trend']),
            "momentum_analysis": self._build_momentum_analysis(analysis['momentum']),
            "volatility_analysis": self._build_volatility_analysis(analysis['volatility']),
            "volume_analysis": self._build_volume_analysis(analysis['volume']),
            "open_interest_analysis": self._build_open_interest_analysis(analysis['open_interest']),
            "support_resistance_analysis": self._build_support_resistance_analysis(analysis['support_resistance']),
            "summary": self._build_summary(analysis),
            "disclaimer": "本报告仅供参考，投资有风险，决策需谨慎"
        }
    
    def _build_trend_analysis(self, trend):
        """构建趋势分析数据"""
        return {
            "overall_trend": {
                "direction": trend['overall_direction_qualitative'],
                "strength": round(trend['trend_strength_quantitative'], 1),
                "description": f"{trend['overall_direction_qualitative']}趋势{self._get_strength_description(trend['trend_strength_quantitative'])}"
            },
            "multi_period_trend": {
                "short_term": trend['short_term'],
                "medium_term": trend['medium_term'],
                "long_term": trend['long_term']
            }
        }
    
    def _build_momentum_analysis(self, momentum):
        """构建动量分析数据"""
        momentum_desc = self._explain_momentum(momentum.get('overall_momentum_qualitative', ''))
        consistency_note = momentum.get('consistency_note', '')
        
        return {
            "market_momentum": momentum_desc,
            "consistency_statement": consistency_note
        }
    
    def _build_volatility_analysis(self, volatility):
        """构建波动性分析数据"""
        # 获取风险控制数据
        risk_control = self._extract_risk_control_data(volatility)
        
        return {
            "volatility_level": volatility.get('level', '正常'),
            "volatility_description": self._get_volatility_description(volatility.get('level', '正常')),
            "historical_volatility": round(volatility.get('historical_volatility', 0), 4),
            "atr_volatility": round(volatility.get('atr_volatility', 0), 4),
            "volatility_percentile": f"{volatility.get('percentile', 0):.1f}%",
            "percentile_interpretation": self._get_percentile_interpretation(volatility.get('percentile', 0)),
            "risk_control": risk_control
        }
    
    def _build_volume_analysis(self, volume):
        """构建成交量分析数据"""
        minute_analysis = volume.get('minute_analysis', {})
        
        volume_status = self._explain_volume_status(minute_analysis.get('status', ''))
        pv_relation = self._explain_price_volume_relation(minute_analysis.get('price_volume_relation', ''))
        
        return {
            "volume_status": volume_status,
            "price_volume_relation": pv_relation,
            "obv_divergence": self._extract_obv_divergence(pv_relation)
        }
    
    def _build_open_interest_analysis(self, open_interest):
        """构建持仓量分析数据"""
        if isinstance(open_interest, dict) and 'summary' in open_interest:
            if open_interest['summary'] == '无持仓量数据':
                return {
                    "status": "无持仓量数据",
                    "signal": "无",
                    "change_rate_last_10_days": 0
                }
        
        # 解析持仓量变化信息
        change_rate = open_interest.get('change_rate', 0)
        status_desc, signal = self._parse_open_interest_status(open_interest)
        
        return {
            "status": status_desc,
            "signal": signal,
            "change_rate_last_10_days": round(change_rate, 2)
        }
    
    def _build_support_resistance_analysis(self, sr_data):
        """构建支撑压力位分析数据"""
        current_price = sr_data.get('current_price', 0)
        
        # 提取压力位和支撑位
        resistance_levels = self._extract_resistance_levels(sr_data)
        support_levels = self._extract_support_levels(sr_data)
        
        # 生成突破分析
        breakout_analysis = self._generate_breakout_analysis(resistance_levels, support_levels, current_price)
        
        return {
            "current_price": current_price,
            "resistance_levels": resistance_levels,
            "support_levels": support_levels,
            "breakout_analysis": breakout_analysis
        }
    
    def _build_summary(self, analysis):
        """构建综合总结"""
        # 提取关键信号
        key_signals = self._extract_key_signals(analysis)
        
        # 提取风险警告
        risk_warnings = self._extract_risk_warnings(analysis)
        
        # 生成综合结论
        comprehensive_result = self._generate_comprehensive_result(analysis)
        
        return {
            "comprehensive_result": comprehensive_result,
            "key_signals": key_signals,
            "risk_warnings": risk_warnings
        }
    
    # 辅助方法
    def _get_strength_description(self, strength):
        """获取趋势强度描述"""
        if strength >= 0.8:
            return "较为确定"
        elif strength >= 0.6:
            return "相对明确"
        elif strength >= 0.4:
            return "一般"
        else:
            return "不明确"
    
    def _explain_momentum(self, momentum_qualitative):
        """解释动量状态"""
        if not momentum_qualitative:
            return "无明显的买卖信号，价格缺乏明确的上涨或下跌动力"
        
        momentum_explanations = {
            '强烈看多': '市场动量强劲向上，多头力量占据主导地位',
            '看多': '市场动量偏向上涨，买盘力量较强',
            '中性': '市场动量平衡，多空力量相当',
            '看空': '市场动量偏向下跌，卖盘力量较强',
            '强烈看空': '市场动量强劲向下，空头力量占据主导地位'
        }
        
        return momentum_explanations.get(momentum_qualitative, "无明显的买卖信号，价格缺乏明确的上涨或下跌动力")
    
    def _get_volatility_description(self, level):
        """获取波动性描述"""
        descriptions = {
            '极低': '市场波动极小，价格变化非常平缓，适合稳健投资',
            '较低': '市场波动较小，价格变化平缓，适合稳健投资',
            '正常': '市场波动正常，价格变化适中，风险可控',
            '较高': '市场波动较大，价格变化剧烈，需要谨慎操作',
            '极高': '市场波动极大，价格变化非常剧烈，风险很高'
        }
        return descriptions.get(level, '市场波动正常，价格变化适中，风险可控')
    
    def _get_percentile_interpretation(self, percentile):
        """获取百分位解释"""
        if percentile >= 80:
            return "当前波动率偏高，市场活跃度较强"
        elif percentile >= 60:
            return "当前波动率适中，市场活跃度正常"
        elif percentile >= 40:
            return "当前波动率偏低，市场相对平静"
        else:
            return "当前波动率较低，市场较为平静"

    def _extract_risk_control_data(self, volatility):
        """提取风险控制数据"""
        # 从波动性分析中提取止损止盈价格
        stop_loss_profit = volatility.get('stop_loss_profit', {})

        return {
            "long_stop_loss": round(stop_loss_profit.get('long_stop_loss', 0), 2),
            "long_take_profit": round(stop_loss_profit.get('long_take_profit', 0), 2),
            "short_stop_loss": round(stop_loss_profit.get('short_stop_loss', 0), 2),
            "short_take_profit": round(stop_loss_profit.get('short_take_profit', 0), 2),
            "avg_fluctuation_last_10_days": round(stop_loss_profit.get('atr_value', 0), 2)
        }

    def _explain_volume_status(self, status):
        """解释成交量状态"""
        status_explanations = {
            '成交量正常': '成交量正常，市场参与度处于常规水平',
            '成交量偏高': '成交量偏高，市场参与度较为活跃',
            '成交量偏低': '成交量偏低，市场参与度不足',
            '成交量异常': '成交量异常，需要关注市场变化'
        }
        return status_explanations.get(status, '成交量正常，市场参与度处于常规水平')

    def _explain_price_volume_relation(self, relation):
        """解释价量关系"""
        if 'OBV与价格走势一致' in relation:
            return "价量配合，价格与成交量走势配合良好，趋势相对可靠"
        elif 'OBV与价格走势背离' in relation:
            return "价量背离，价格与成交量走势不一致，可能预示趋势转变"
        else:
            return "价量关系正常，市场运行平稳"

    def _extract_obv_divergence(self, pv_relation):
        """提取OBV背离信息"""
        if "背离" in pv_relation:
            return "OBV与价格走势背离，可能预示趋势转变。OBV是成交量能量指标，当价格上涨时成交量减少、价格下跌时成交量增加，说明资金流向与价格变化方向相反，这种背离往往预示着趋势可能发生反转"
        else:
            return "OBV与价格走势一致，验证当前趋势。OBV是成交量能量指标，当价格上涨时成交量增加、价格下跌时成交量减少，说明资金流向与价格变化方向一致，验证了当前趋势的可靠性"

    def _parse_open_interest_status(self, open_interest):
        """解析持仓量状态"""
        change_rate = open_interest.get('change_rate', 0)

        if change_rate > 5:
            status = "持仓量显著增长，呈现价涨量增格局"
            signal = "多头建仓"
        elif change_rate > 0:
            status = "持仓量小幅增长，市场参与度提升"
            signal = "多头建仓"
        elif change_rate < -5:
            status = "持仓量显著下降，呈现价跌量减格局"
            signal = "多头平仓"
        elif change_rate < 0:
            status = "持仓量小幅下降，市场参与度下降"
            signal = "多头平仓"
        else:
            status = "持仓量基本稳定，市场观望情绪浓厚"
            signal = "观望"

        return status, signal

    def _extract_resistance_levels(self, sr_data):
        """提取压力位数据"""
        resistance_levels = []

        # 从支撑压力位数据中提取压力位信息
        resistance_info = sr_data.get('resistance_levels', {})
        if isinstance(resistance_info, dict):
            # 最近压力位
            if 'nearest' in resistance_info:
                nearest = resistance_info['nearest']
                resistance_levels.append({
                    "type": "最近压力位",
                    "price": round(nearest.get('price', 0), 2),
                    "distance": f"{nearest.get('distance_pct', 0):.1f}%",
                    "strength": nearest.get('strength', 0)
                })

            # 最强压力位
            if 'strongest' in resistance_info:
                strongest = resistance_info['strongest']
                resistance_levels.append({
                    "type": "最强压力位",
                    "price": round(strongest.get('price', 0), 2),
                    "distance": f"{strongest.get('distance_pct', 0):.1f}%",
                    "strength": strongest.get('strength', 0)
                })

        return resistance_levels

    def _extract_support_levels(self, sr_data):
        """提取支撑位数据"""
        support_levels = []

        # 从支撑压力位数据中提取支撑位信息
        support_info = sr_data.get('support_levels', {})
        if isinstance(support_info, dict):
            # 最近支撑位
            if 'nearest' in support_info:
                nearest = support_info['nearest']
                support_levels.append({
                    "type": "最近支撑位",
                    "price": round(nearest.get('price', 0), 2),
                    "distance": f"{nearest.get('distance_pct', 0):.1f}%",
                    "strength": nearest.get('strength', 0)
                })

            # 最强支撑位
            if 'strongest' in support_info:
                strongest = support_info['strongest']
                support_levels.append({
                    "type": "最强支撑位",
                    "price": round(strongest.get('price', 0), 2),
                    "distance": f"{strongest.get('distance_pct', 0):.1f}%",
                    "strength": strongest.get('strength', 0)
                })

        return support_levels

    def _generate_breakout_analysis(self, resistance_levels, support_levels, current_price):
        """生成突破分析"""
        analysis_parts = []

        # 分析压力位
        for resistance in resistance_levels:
            if resistance['type'] == '最近压力位':
                distance = abs(float(resistance['distance'].replace('%', '')))
                if distance < 2:
                    analysis_parts.append(f"接近关键压力位{resistance['price']}，但成交量不足，突破存疑")

        # 分析支撑位
        for support in support_levels:
            if support['type'] == '最近支撑位':
                distance = abs(float(support['distance'].replace('%', '')))
                if distance < 2:
                    analysis_parts.append(f"接近关键支撑位{support['price']}，成交量正常，支撑有效")

        return "；".join(analysis_parts) if analysis_parts else "价格运行平稳，暂无明显突破信号"

    def _extract_key_signals(self, analysis):
        """提取关键信号"""
        key_signals = []

        # 从趋势分析中提取信号
        trend = analysis.get('trend', {})
        ma_analysis = trend.get('ma_analysis', {})
        if ma_analysis.get('status') == '完美多头排列':
            key_signals.append("上涨信号：各周期均线呈完美多头排列，价格位于所有均线之上，上涨趋势非常强劲")
        elif ma_analysis.get('status') == '多头排列':
            key_signals.append("上涨信号：均线呈多头排列，价格位于主要均线之上，上涨趋势较强")
        elif ma_analysis.get('status') == '空头排列':
            key_signals.append("下跌信号：均线呈空头排列，价格位于主要均线之下，下跌趋势较强")

        # 从波动性分析中提取布林带信号
        volatility = analysis.get('volatility', {})
        bollinger_desc = volatility.get('bollinger_description', '')
        if '布林带' in bollinger_desc:
            key_signals.append(f"突破信号：{bollinger_desc}")

        # 如果没有明显信号，添加默认描述
        if not key_signals:
            key_signals.append("市场信号不明确，建议观望等待更清晰的方向信号")

        return key_signals

    def _extract_risk_warnings(self, analysis):
        """提取风险警告"""
        risk_warnings = []

        # 从动量分析中提取风险
        momentum = analysis.get('momentum', {})
        if momentum.get('consistency_note'):
            risk_warnings.append("市场方向不明确，可能出现反复震荡，不适合追涨杀跌")

        # 从成交量分析中提取风险
        volume = analysis.get('volume', {})
        minute_analysis = volume.get('minute_analysis', {})
        if 'OBV与价格走势背离' in minute_analysis.get('price_volume_relation', ''):
            risk_warnings.append("价格与成交量走势不一致，可能预示趋势即将转变")

        # 从支撑压力位分析中提取风险
        sr_data = analysis.get('support_resistance', {})
        support_levels = self._extract_support_levels(sr_data)
        for support in support_levels:
            if support['type'] == '最近支撑位':
                distance = abs(float(support['distance'].replace('%', '')))
                if distance < 2:
                    risk_warnings.append("价格接近重要支撑位，需要关注是否会跌破支撑")
                    break

        # 如果没有明显风险，添加通用风险提示
        if not risk_warnings:
            risk_warnings.append("注意控制仓位，设置合理的止损止盈")

        return risk_warnings

    def _generate_comprehensive_result(self, analysis):
        """生成综合结论"""
        trend = analysis.get('trend', {})
        momentum = analysis.get('momentum', {})
        volatility = analysis.get('volatility', {})

        # 基于趋势强度和动量状态生成建议
        trend_strength = trend.get('trend_strength_quantitative', 0)
        trend_direction = trend.get('overall_direction_qualitative', '')

        if trend_strength >= 0.7:
            if '上升' in trend_direction:
                return "建议适度做多，趋势较为明确"
            elif '下降' in trend_direction:
                return "建议谨慎做空，注意风险控制"
            else:
                return "建议观望，等待趋势明确"
        elif trend_strength >= 0.5:
            return "建议谨慎操作，趋势不够明确"
        else:
            return "建议谨慎做多，注意风险控制"
