"""
远程数据加载器
通过HTTP请求获取期货K线数据
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime
import logging
from io import StringIO
import time

logger = logging.getLogger(__name__)

class RemoteDataLoader:
    """远程数据加载器 - 通过HTTP请求获取数据"""
    
    def __init__(self, base_url="http://117.72.196.97:9090/market-data"):
        """初始化远程数据加载器"""
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = 30  # 30秒超时
        self.last_request_id = None  # 存储最后一次请求的ID
        
        # 周期映射
        self.period_mapping = {
            '15分钟线': 'Minute_15',
            '日线': 'Day',
            '分钟线': 'Minute_1',
            '5分钟线': 'Minute_5',
            '30分钟线': 'Minute_30',
            '小时线': 'Hour',
            '周线': 'Week',
            '月线': 'Month'
        }
        
        # 支持的交易所
        self.supported_exchanges = ['SHFE', 'DCE', 'ZCE', 'CFFEX', 'GFEX']
    
    def list_available_exchanges(self):
        """列出可用的交易所"""
        return self.supported_exchanges.copy()
    
    def list_contracts_by_exchange(self, exchange):
        """
        列出指定交易所的合约
        注意：由于是远程API，这里返回常见合约列表
        实际使用中可能需要调用专门的合约列表API
        """
        common_contracts = {
            'CFFEX': ['IF2509', 'IC2509', 'IH2509', 'IM2509', 'T2509', 'TF2509', 'TS2509'],
            'SHFE': ['ag2508', 'au2508', 'cu2508', 'al2508', 'zn2508', 'pb2508', 'ni2508'],
            'DCE': ['a2509', 'm2509', 'y2509', 'c2509', 'cs2509', 'p2509', 'l2509'],
            'ZCE': ['TA509', 'MA509', 'CF509', 'SR509', 'RM509', 'OI509', 'FG509'],
            'GFEX': ['si2509', 'lc2509', 'bc2509']
        }
        return common_contracts.get(exchange, [])
    
    def get_available_periods(self, exchange, contract):
        """获取可用的数据周期"""
        return list(self.period_mapping.keys())
    
    def _build_url(self, exchange, contract, period):
        """构建请求URL"""
        period_suffix = self.period_mapping.get(period, 'Day')
        url = f"{self.base_url}/{exchange}/{contract}_{period_suffix}.txt"
        return url
    
    def _fetch_data_from_url(self, url, max_retries=3):
        """从URL获取数据"""
        for attempt in range(max_retries):
            try:
                # logger.info(f"请求数据URL: {url} (尝试 {attempt + 1}/{max_retries})")

                response = self.session.get(url)

                if response.status_code == 200:
                    # logger.info(f"成功获取数据，响应大小: {len(response.text)} 字符")

                    # 检查响应头中是否有request ID相关信息
                    request_id = None
                    for header_name in ['X-Request-ID', 'Request-ID', 'X-Trace-ID', 'Trace-ID']:
                        if header_name in response.headers:
                            request_id = response.headers[header_name]
                            # logger.info(f"从响应头获取到请求ID: {request_id}")
                            break

                    # 将request_id存储到实例变量中，供后续使用
                    self.last_request_id = request_id

                    return response.text
                elif response.status_code == 404:
                    # logger.error(f"数据文件不存在: {url}")
                    return None
                else:
                    # logger.warning(f"请求失败，状态码: {response.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(1)  # 重试前等待1秒
                        continue
                    else:
                        # logger.error(f"获取数据失败: {response.status_code} - {response.text}")
                        return None
                        
            except requests.exceptions.Timeout:
                # logger.warning(f"请求超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    # logger.error("请求超时，已达到最大重试次数")
                    return None
                    
            except requests.exceptions.ConnectionError:
                # logger.warning(f"连接错误 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    # logger.error("连接失败，已达到最大重试次数")
                    return None
                    
            except Exception as e:
                # logger.error(f"请求异常: {str(e)}")
                return None
        
        return None
    
    def _parse_raw_data(self, raw_data, exchange):
        """解析原始数据"""
        if not raw_data:
            return None

        # 数据是用分号分隔的单行格式
        records = raw_data.strip().split(';')
        if len(records) < 2:  # 至少需要有数据记录
            # logger.warning("数据文件为空或格式不正确")
            return None

        # 解析数据记录
        parsed_records = []
        original_count = len(records)
        cleaned_count = 0
        invalid_count = 0

        for record_num, record in enumerate(records, 1):
            try:
                if not record.strip():  # 跳过空记录
                    continue

                # 解析记录
                parsed_record = self._parse_record(record.strip())
                if parsed_record:
                    parsed_records.append(parsed_record)
                    cleaned_count += 1
                else:
                    invalid_count += 1
                    if record_num <= 3:  # 只显示前3条失败记录的详细信息
                        print(f"    ⚠️ 第{record_num}条记录解析失败: {record[:100]}...")

            except Exception as e:
                # logger.debug(f"解析第{record_num}条记录失败: {str(e)}")
                invalid_count += 1
                if record_num <= 3:  # 只显示前3条异常记录的详细信息
                    print(f"    ❌ 第{record_num}条记录异常: {str(e)}")
                continue
        
        if not parsed_records:
            # logger.error("没有有效的数据记录")
            return None
        
        # 转换为DataFrame
        df = pd.DataFrame(parsed_records)
        
        # 数据验证和清洗
        df = self._validate_and_clean_data(df)
        
        # 清洗统计
        final_count = len(df)
        clean_rate = (final_count / original_count * 100) if original_count > 0 else 0
        
        # print(f"    📊 数据清洗统计:")
        # print(f"      原始记录: {original_count}")
        # print(f"      有效记录: {final_count}")
        # print(f"      清理脏数据: {original_count - cleaned_count}")
        # print(f"      移除无效记录: {cleaned_count - final_count}")
        # print(f"      清洗率: {clean_rate:.2f}%")
        
        # logger.info(f"数据清洗完成: 原始记录 {original_count}, 有效记录 {final_count}, "
        #            f"清理脏数据 {original_count - cleaned_count}, 移除无效记录 {cleaned_count - final_count}, "
        #            f"清洗率 {clean_rate:.2f}%")
        
        return df
    

    
    def _parse_record(self, record):
        """解析单条记录 - 处理逗号分隔的格式"""
        try:
            fields = record.split(',')
            if len(fields) < 13:
                return None

            # 解析各字段 (格式: 交易所,合约,日期,时间,毫秒,开盘,最高,最低,收盘,成交量,持仓量,前持仓量,日成交量,日成交额)
            parsed = {
                'contract_id': fields[1].strip(),  # 合约代码
                'trade_date': fields[2].strip(),   # 交易日期
                'update_time': fields[3].strip(),  # 更新时间
                'update_ms': fields[4].strip(),    # 毫秒
                'open': float(fields[5]) if fields[5].strip() else 0.0,
                'high': float(fields[6]) if fields[6].strip() else 0.0,
                'low': float(fields[7]) if fields[7].strip() else 0.0,
                'close': float(fields[8]) if fields[8].strip() else 0.0,
                'volume': int(float(fields[9])) if fields[9].strip() else 0,
                'open_interest': int(float(fields[10])) if fields[10].strip() else 0,
                'prev_open_interest': int(float(fields[11])) if fields[11].strip() else 0,
                'daily_volume': int(float(fields[12])) if fields[12].strip() else 0,
                'daily_amount': float(fields[13]) if len(fields) > 13 and fields[13].strip() else 0.0
            }
            
            # 基本数据验证
            if (parsed['open'] <= 0 or parsed['high'] <= 0 or 
                parsed['low'] <= 0 or parsed['close'] <= 0):
                return None
            
            if parsed['high'] < parsed['low']:
                return None
            
            if (parsed['high'] < max(parsed['open'], parsed['close']) or 
                parsed['low'] > min(parsed['open'], parsed['close'])):
                return None
            
            # 异常波动检查（日内波动超过50%认为异常）
            daily_range = (parsed['high'] - parsed['low']) / parsed['low']
            if daily_range > 0.5:
                return None
            
            return parsed
            
        except (ValueError, IndexError) as e:
            return None
    
    def _validate_and_clean_data(self, df):
        """验证和清洗数据"""
        if df.empty:
            return df
        
        initial_count = len(df)
        print(f"    🔧 开始数据验证和清洗，初始记录数: {initial_count}")
        
        # 移除重复记录
        df = df.drop_duplicates(subset=['trade_date', 'update_time'], keep='last')
        after_dedup_count = len(df)
        print(f"    🔧 去重后记录数: {after_dedup_count} (移除 {initial_count - after_dedup_count} 条重复)")
        
        # 按时间排序
        df = df.sort_values(['trade_date', 'update_time']).reset_index(drop=True)
        
        # 处理时间字段
        df['datetime'] = pd.to_datetime(df['trade_date'] + ' ' + df['update_time'], 
                                       format='%Y%m%d %H:%M:%S', errors='coerce')
        
        # 移除时间解析失败的记录
        df = df.dropna(subset=['datetime'])
        
        final_count = len(df)
        print(f"    🔧 时间验证后记录数: {final_count} (移除 {after_dedup_count - final_count} 条无效时间)")
        
        if final_count < initial_count:
            logger.info(f"移除重复和无效时间记录: {initial_count - final_count} 条")
        
        return df
    
    def load_contract_data(self, exchange, contract, period='日线'):
        """加载指定合约的数据"""
        try:
            # 构建URL
            url = self._build_url(exchange, contract, period)
            # print(f"    📡 请求URL: {url}")
            
            # 获取原始数据
            raw_data = self._fetch_data_from_url(url)
            if not raw_data:
                # logger.error(f"无法获取数据: {exchange}/{contract} - {period}")
                # print(f"    ❌ 无法获取数据: {url}")
                return None
            
            # 解析数据
            df = self._parse_raw_data(raw_data, exchange)
            if df is None or df.empty:
                # logger.error(f"数据解析失败: {exchange}/{contract} - {period}")
                return None
            
            # logger.info(f"成功加载 {len(df)} 条数据记录")
            return df
            
        except Exception as e:
            # logger.error(f"加载数据异常: {str(e)}")
            return None
    
    def get_last_request_id(self):
        """获取最后一次请求的ID"""
        return self.last_request_id

    def test_connection(self, exchange, contract):
        """测试连接"""
        try:
            # 使用指定的交易所和合约进行测试
            test_urls = [
                f"{self.base_url}/{exchange}/{contract}_Day.txt",
                f"{self.base_url}/{exchange}/{contract}_Minute_15.txt"
            ]
            
            for test_url in test_urls:
                try:
                    response = self.session.get(test_url, timeout=10)
                    if response.status_code == 200:
                        logger.info(f"连接测试成功: {test_url}")
                        return True
                except Exception as e:
                    logger.debug(f"测试URL失败: {test_url} - {str(e)}")
                    continue
            
            logger.warning(f"无法访问 {exchange}/{contract} 的数据文件")
            return False
        except Exception as e:
            logger.error(f"连接测试异常: {str(e)}")
            return False
