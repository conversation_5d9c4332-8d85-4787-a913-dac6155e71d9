#!/usr/bin/env python3
"""
期货技术分析系统 v1.0 测试脚本
测试仅使用日K线数据的分析功能
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from remote_data_loader import RemoteDataLoader
from numpy_technical_indicators import NumpyTechnicalIndicators
from enhanced_analysis_engine import EnhancedAnalysisEngine
from json_report_generator import JsonReportGenerator

def test_daily_only_analysis():
    """测试仅使用日K线数据的分析功能"""
    print("🚀 期货技术分析系统 v1.0 测试")
    print("🔬 测试仅使用日K线数据的分析功能")
    print("="*60)
    
    # 测试参数
    exchange = 'CFFEX'
    contract = 'IF2509'
    
    try:
        # 初始化组件
        print("🔗 初始化组件...")
        loader = RemoteDataLoader()
        calculator = NumpyTechnicalIndicators()
        engine = EnhancedAnalysisEngine()
        
        # 加载日线数据
        print(f"📊 加载日线数据: {exchange}/{contract}")
        daily_data = loader.load_contract_data(exchange, contract, '日线')
        
        if daily_data is None:
            print("❌ 无法获取日线数据")
            return False
            
        print(f"✅ 成功加载 {len(daily_data)} 条日线数据")
        
        # 计算技术指标
        print("🔧 计算技术指标...")
        daily_indicators = calculator.calculate_all_indicators(daily_data)
        
        if daily_indicators is None:
            print("❌ 技术指标计算失败")
            return False
            
        print("✅ 技术指标计算完成")
        
        # 生成分析
        print("📝 生成综合分析...")
        analysis = engine.generate_comprehensive_analysis(
            exchange, contract, daily_data, daily_indicators
        )
        
        if analysis is None:
            print("❌ 分析生成失败")
            return False
            
        print("✅ 综合分析生成完成")
        
        # 显示关键结果
        print("\n📈 分析结果摘要:")
        print(f"  合约: {analysis['exchange']}/{analysis['contract']}")
        print(f"  当前价格: {analysis['current_price']:.2f}")
        print(f"  数据周期: {analysis['data_period']}")
        print(f"  日线数据条数: {analysis['total_records']['daily']}")
        
        # 趋势分析
        trend = analysis['trend']
        print(f"\n📊 趋势分析:")
        print(f"  整体方向: {trend['overall_direction_qualitative']}")
        print(f"  趋势强度: {trend['trend_strength_quantitative']:.3f}")
        print(f"  强度等级: {trend['strength_level']}")
        print(f"  短期趋势: {trend['short_term']}")
        print(f"  中期趋势: {trend['medium_term']}")
        print(f"  长期趋势: {trend['long_term']}")
        
        # 动量分析
        momentum = analysis['momentum']
        print(f"\n⚡ 动量分析:")
        print(f"  整体动量: {momentum['overall_momentum_qualitative']}")
        
        # 波动性分析
        volatility = analysis['volatility']
        print(f"\n📈 波动性分析:")
        print(f"  波动性等级: {volatility['level']}")
        print(f"  历史波动率: {volatility['historical_volatility']:.4f}")
        print(f"  ATR波动率: {volatility['atr_volatility']:.4f}")
        
        # 成交量分析
        volume = analysis['volume']
        print(f"\n📊 成交量分析:")
        print(f"  日线分析状态: {volume['daily_analysis']['status']}")
        print(f"  价量关系: {volume['daily_analysis']['price_volume_relation']}")
        
        # 综合评估
        assessment = analysis['overall_assessment']
        print(f"\n🎯 综合评估:")
        print(f"  综合评分: {assessment['score']}/100")
        print(f"  投资建议: {assessment['investment_recommendation']}")
        
        # 保存测试结果
        output_dir = os.path.join(os.path.dirname(__file__), 'test_results')
        os.makedirs(output_dir, exist_ok=True)
        
        report_generator = JsonReportGenerator(output_dir)
        report_path = report_generator.save_json_report(analysis, 'test_v1.0')
        
        print(f"\n💾 测试结果已保存: {report_path}")
        print("✅ v1.0版本测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_original():
    """与原版本进行对比测试（如果可用）"""
    print("\n🔄 对比测试...")
    print("注意: v1.0版本仅使用日K线数据，与原版本的分析结果可能有所不同")
    print("这是正常的，因为原版本结合了15分钟K线和日K线数据")

if __name__ == "__main__":
    success = test_daily_only_analysis()
    
    if success:
        compare_with_original()
        print("\n🎉 所有测试完成！")
        print("📝 v1.0版本特点:")
        print("  - 仅使用日K线数据")
        print("  - 简化了分析逻辑")
        print("  - 保持了完整的技术指标计算")
        print("  - 适合中长期分析")
    else:
        print("\n❌ 测试失败，请检查错误信息")
        sys.exit(1)
