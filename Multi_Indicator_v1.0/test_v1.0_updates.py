#!/usr/bin/env python3
"""
测试v1.0版本的更新功能
验证历史波动率限制、type参数支持和文件命名
"""

import subprocess
import sys
import os
import json

def test_v1_0_updates():
    """测试v1.0版本的更新功能"""
    print("🚀 测试v1.0版本更新功能")
    print("📋 验证历史波动率限制、type参数和文件命名")
    print("="*60)
    
    # 测试URL - 包含完整的req_id#func_id#type参数
    test_url = "http://117.72.196.97:9090/market-data/CFFEX/IF2509_Day.txt#test_v1.0#3001#4"
    
    print(f"📈 测试URL: {test_url}")
    print("📊 预期结果:")
    print("  - type=4 (日K线)")
    print("  - 历史波动率使用最多60天数据")
    print("  - 生成文件名: CFFEX_IF2509_4.json")
    print()
    
    try:
        # 运行分析器
        result = subprocess.run([
            sys.executable, 
            'analyzer_numpy_json.py', 
            test_url
        ], capture_output=True, text=True, timeout=90)
        
        if result.returncode == 0:
            print("✅ 分析执行成功！")
            
            # 检查输出信息
            output = result.stdout
            
            # 验证type参数处理
            if "数据类型: 4 (日K线)" in output:
                print("  ✓ Type参数处理正确 (type=4)")
            else:
                print("  ❌ Type参数处理失败")
                return False
            
            # 验证功能编号
            if "功能编号: 3001" in output:
                print("  ✓ 功能编号处理正确")
            else:
                print("  ❌ 功能编号处理失败")
                return False
            
            # 验证数据加载
            if "日线数据条数" in output:
                print("  ✓ 日线数据加载正常")
            else:
                print("  ❌ 日线数据加载失败")
                return False
            
            return True
            
        else:
            print("❌ 分析执行失败！")
            print(f"返回码: {result.returncode}")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            if result.stdout:
                print(f"输出信息: {result.stdout}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 分析超时！")
        return False
    except Exception as e:
        print(f"❌ 运行异常: {str(e)}")
        return False

def verify_json_output():
    """验证JSON输出格式和文件命名"""
    print("\n🔍 验证JSON输出")
    print("="*30)
    
    results_dir = os.path.join(os.path.dirname(__file__), 'analysis_results')
    if not os.path.exists(results_dir):
        print("❌ 结果目录不存在")
        return False
    
    files = os.listdir(results_dir)
    target_file = "CFFEX_IF2509_4.json"
    
    if target_file not in files:
        print(f"❌ 未找到目标文件: {target_file}")
        print(f"📁 现有文件: {files}")
        return False
    
    print(f"✅ 找到目标文件: {target_file}")
    
    # 验证JSON内容
    filepath = os.path.join(results_dir, target_file)
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("📄 验证JSON内容:")
        
        # 验证基本字段
        required_fields = ['func_id', 'req_id', 'type', 'error_id', 'data']
        for field in required_fields:
            if field in data:
                print(f"  ✓ 包含字段: {field} = {data[field]}")
            else:
                print(f"  ❌ 缺少字段: {field}")
                return False
        
        # 验证type值
        if data.get('type') == '4':
            print("  ✓ type值正确 (日K线)")
        else:
            print(f"  ❌ type值错误: {data.get('type')}")
            return False
        
        # 验证func_id
        if data.get('func_id') == '3001':
            print("  ✓ func_id正确")
        else:
            print(f"  ❌ func_id错误: {data.get('func_id')}")
            return False
        
        # 验证req_id
        if data.get('req_id') == 'test_v1.0':
            print("  ✓ req_id正确")
        else:
            print(f"  ❌ req_id错误: {data.get('req_id')}")
            return False
        
        # 验证数据结构
        if 'data' in data and 'total_records' in data['data']:
            records = data['data']['total_records']
            if 'daily' in records:
                count = records['daily']
                print(f"  ✓ 日线数据条数: {count}")
            else:
                print("  ❌ 缺少daily记录")
                return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ JSON解析失败: {str(e)}")
        return False

def test_historical_volatility():
    """测试历史波动率计算是否限制在60天"""
    print("\n📊 测试历史波动率计算")
    print("="*30)
    
    # 这个测试需要检查代码逻辑，因为我们无法直接验证内部计算
    # 但我们可以检查代码是否包含了60天限制
    
    try:
        with open('numpy_technical_indicators.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "close_limited = close[-60:]" in content:
            print("✅ 历史波动率计算已限制为最多60天")
            return True
        else:
            print("❌ 历史波动率计算未找到60天限制")
            return False
            
    except Exception as e:
        print(f"❌ 代码检查失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🎯 v1.0版本更新功能测试")
    print("📋 测试目标:")
    print("  1. 历史波动率最多使用60天数据")
    print("  2. 支持type参数(type=4表示日K线)")
    print("  3. 文件命名包含type标识")
    print()
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"📂 当前目录: {current_dir}")
    
    # 检查必要文件
    required_files = [
        'analyzer_numpy_json.py',
        'numpy_technical_indicators.py',
        'json_report_generator.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    print("✅ 所有必要文件都存在")
    
    # 运行测试
    test1_ok = test_v1_0_updates()
    test2_ok = verify_json_output()
    test3_ok = test_historical_volatility()
    
    # 总结结果
    print("\n" + "="*60)
    print("🎉 测试总结")
    print(f"📊 功能测试: {'✅ 通过' if test1_ok else '❌ 失败'}")
    print(f"📁 JSON验证: {'✅ 通过' if test2_ok else '❌ 失败'}")
    print(f"📈 波动率测试: {'✅ 通过' if test3_ok else '❌ 失败'}")
    
    if test1_ok and test2_ok and test3_ok:
        print("\n🎉 所有测试通过！v1.0版本更新成功")
        print("\n📝 更新特点总结:")
        print("  ✓ 历史波动率限制为最多60天数据")
        print("  ✓ 支持完整的type参数(req_id#func_id#type)")
        print("  ✓ 文件命名包含type标识(交易所_合约_type.json)")
        print("  ✓ JSON输出包含type字段")
        print("  ✓ 仅支持日K线数据(type=4)")
        return True
    else:
        print("\n❌ 部分测试失败")
        print("🔧 请检查:")
        print("  1. URL解析是否正确处理type参数")
        print("  2. 历史波动率计算是否限制60天")
        print("  3. JSON文件命名是否包含type")
        print("  4. JSON输出是否包含type字段")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
