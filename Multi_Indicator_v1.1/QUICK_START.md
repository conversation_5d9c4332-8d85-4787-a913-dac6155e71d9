# 期货技术分析系统 - NumPy最小版本

## 🚀 快速开始 (3步完成)

### 1️⃣ 安装依赖
```bash
pip install numpy scipy pandas requests
```

### 2️⃣ 运行分析
```bash
python analyzer_numpy_json.py CFFEX/IF2509
```

### 3️⃣ 查看结果
生成的JSON文件保存在 `analysis_results/` 目录

---

## 📁 文件说明

| 文件 | 说明 |
|------|------|
| `analyzer_numpy_json.py` | 主分析器 (JSON输出) |
| `setup_and_test.py` | 一键安装和测试 |
| `example_usage.py` | 交互式使用示例 |
| `test_minimal_version.py` | 功能测试脚本 |
| `requirements.txt` | 依赖包列表 |

## 🎯 支持的合约

- **CFFEX**: IF2509, IC2509, IH2509 (股指期货)
- **SHFE**: au2510, ag2510, cu2510 (贵金属、有色金属)
- **DCE**: a2509, m2509, y2509 (农产品)
- **ZCE**: MA509, TA509, CF509 (化工、纺织)
- **GFEX**: lc2509 (工业硅)

## 📊 JSON输出格式

```json
{
  "func_id": "3001",
  "req_id": "请求ID",
  "error_id": "1",
  "data": {
    "contract_code": "合约代码",
    "trend_analysis": {...},
    "momentum_analysis": {...},
    "volatility_analysis": {...},
    "volume_analysis": {...},
    "support_resistance_analysis": {...},
    "summary": {...}
  }
}
```

## ⚡ 一键安装测试

```bash
python setup_and_test.py
```

## 🔧 命令行选项

```bash
# 指定请求ID
python analyzer_numpy_json.py CFFEX/IF2509 --req-id "my-request"

# 指定输出目录
python analyzer_numpy_json.py SHFE/au2510 --output-dir reports

# 详细输出
python analyzer_numpy_json.py DCE/a2509 --verbose
```

## ⚠️ 注意事项

1. 需要稳定的网络连接
2. Python 3.7+ 版本
3. 合约代码格式: `交易所/合约代码`
4. 仅供参考，投资有风险

---

**版本**: NumPy最小版本 v0.6  
**技术栈**: Python + NumPy + SciPy  
**输出格式**: JSON
