# 期货技术分析系统 v1.1 - 15分钟K线专用版本

## 📋 项目简介

这是期货技术分析系统的v1.1版本，专门设计为仅使用15分钟K线数据进行技术分析，包含运行JSON格式分析所需的核心文件。

### 🎯 特点
- **15分钟K线专用**: 仅使用15分钟K线数据，适合短期分析
- **数据限制**: 最多使用300条15分钟K线数据，确保计算效率
- **智能Type推断**: 根据URL自动推断数据类型，无需手动指定
- **参数简化**: req_id和func_id可选，支持默认值
- **最小依赖**: 只需要 numpy, scipy, requests, pandas
- **JSON输出**: 标准化JSON格式报告，文件名包含type标识
- **自主实现**: 基于NumPy/SciPy的技术指标计算
- **轻量级**: 只包含核心功能文件

## 📁 文件说明

### 核心文件
- **analyzer_numpy_json.py** - 主分析器，JSON输出版本
- **json_report_generator.py** - JSON报告生成器
- **enhanced_analysis_engine.py** - 综合分析引擎
- **remote_data_loader.py** - 远程数据加载器
- **numpy_technical_indicators.py** - NumPy技术指标计算器

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install numpy scipy pandas requests
```

### 2. 基本使用

#### 最简格式（推荐）
```bash
# 系统自动推断type=2（15分钟K线），func_id=3001，req_id从数据获取
python analyzer_numpy_json.py "http://*************:9090/market-data/CFFEX/IF2509_Minute_15.txt"
```

#### 指定req_id
```bash
python analyzer_numpy_json.py "http://*************:9090/market-data/CFFEX/IF2509_Minute_15.txt#my_request_id"
```

#### 完整格式
```bash
python analyzer_numpy_json.py "http://*************:9090/market-data/CFFEX/IF2509_Minute_15.txt#req_id#3001"
```

#### 更多示例
```bash
# 不同交易所的15分钟K线分析
python analyzer_numpy_json.py "http://*************:9090/market-data/DCE/a2509_Minute_15.txt"
python analyzer_numpy_json.py "http://*************:9090/market-data/SHFE/ag2508_Minute_15.txt"
python analyzer_numpy_json.py "http://*************:9090/market-data/ZCE/MA509_Minute_15.txt"

# 指定输出目录
python analyzer_numpy_json.py "http://*************:9090/market-data/CFFEX/IF2509_Minute_15.txt" --output-dir json_reports

# 详细输出模式
python analyzer_numpy_json.py "http://*************:9090/market-data/CFFEX/IF2509_Minute_15.txt" --verbose
```

### 智能参数推断
- **Type自动推断**:
  - `_Minute_15.txt` → type=2 (15分钟K线)
  - `_Day.txt` → type=4 (日K线，v1.1不支持)
- **默认值**:
  - func_id: 默认为 `3001`
  - req_id: 默认从数据加载后获取

### 3. 支持的交易所和合约
- **CFFEX** - 中国金融期货交易所 (如: IF2509, IC2509, IH2509)
- **SHFE** - 上海期货交易所 (如: au2510, ag2510, cu2510)
- **DCE** - 大连商品交易所 (如: a2509, m2509, y2509)
- **ZCE** - 郑州商品交易所 (如: MA509, TA509, CF509)
- **GFEX** - 广州期货交易所 (如: lc2509)

## 📊 JSON输出格式

### 基本结构
```json
{
  "func_id": "3001",
  "req_id": "用户指定或自动生成",
  "error_id": "0",
  "data": {
    "contract_code": "合约代码",
    "analysis_time": "分析时间",
    "trend_analysis": {...},
    "momentum_analysis": {...},
    "volatility_analysis": {...},
    "volume_analysis": {...},
    "open_interest_analysis": {...},
    "support_resistance_analysis": {...},
    "summary": {...},
    "disclaimer": "风险提示"
  }
}
```

### 字段说明
- **func_id**: 固定值 "3001"
- **req_id**: 请求ID，可通过 `--req-id` 参数指定
- **error_id**: "0"表示成功，"1"表示失败
- **data**: 完整的分析数据

## 🔧 技术指标覆盖

### 趋势指标
- 移动平均线 (MA5, MA10, MA20, MA60)
- 指数移动平均线 (EMA)
- 趋势强度计算
- 多周期趋势分析

### 动量指标
- 相对强弱指数 (RSI)
- MACD指标
- KDJ随机指标
- 威廉指标 (WR)

### 波动性指标
- 平均真实波幅 (ATR)
- 布林带 (Bollinger Bands)
- 历史波动率
- 波动率百分位

### 成交量指标
- 能量潮指标 (OBV)
- 价量关系分析
- 成交量趋势

### 支撑压力位
- 动态支撑压力位识别
- 强度评估
- 突破概率分析

## 📈 分析维度

### 1. 趋势分析
- 整体趋势方向和强度
- 短期/中期/长期趋势
- 均线排列状态

### 2. 动量分析
- 市场动量状态
- 买卖信号识别
- 一致性检查

### 3. 波动性分析
- 波动性等级评估
- 风险控制建议
- 止损止盈价格

### 4. 成交量分析
- 成交量状态评估
- 价量关系分析
- OBV背离检测

### 5. 持仓量分析
- 持仓量变化趋势
- 市场情绪判断
- 资金流向分析

### 6. 支撑压力位分析
- 关键价位识别
- 突破概率评估
- 操作建议

### 7. 综合总结
- 投资建议
- 关键信号提取
- 风险警告

## ⚠️ 注意事项

1. **网络连接**: 需要稳定的网络连接获取实时数据
2. **数据延迟**: 数据可能有轻微延迟，请以实际交易价格为准
3. **风险提示**: 本系统仅供参考，投资有风险，决策需谨慎
4. **合约有效性**: 请确保输入的合约代码有效且在交易时间内

## 🔄 输出文件

- **文件格式**: JSON格式
- **文件命名**: `{交易所}_{合约代码}.json`
- **保存位置**: 默认保存在 `analysis_results/` 目录
- **编码格式**: UTF-8

## 📞 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. 合约代码格式是否正确
3. 依赖包是否正确安装
4. Python版本是否为3.7+

## 🎯 适用场景

- **API集成**: 便于与其他系统集成
- **批量分析**: 支持脚本化批量处理
- **数据存储**: JSON格式便于数据库存储
- **程序化交易**: 可集成到自动化交易系统

---

**版本**: NumPy最小版本 v0.6  
**更新时间**: 2025-07-29  
**技术栈**: Python + NumPy + SciPy
