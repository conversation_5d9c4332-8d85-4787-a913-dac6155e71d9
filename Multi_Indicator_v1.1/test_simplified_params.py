#!/usr/bin/env python3
"""
测试简化参数功能
验证智能type推断和默认参数功能
"""

import subprocess
import sys
import os
import json

def test_simplified_params():
    """测试简化参数功能"""
    print("🚀 测试简化参数功能")
    print("📋 验证智能type推断和默认参数")
    print("="*60)
    
    # 测试用例
    test_cases = [
        {
            "name": "最简格式",
            "url": "http://*************:9090/market-data/CFFEX/IF2509_Minute_15.txt",
            "expected_type": "2",
            "expected_func_id": "3001",
            "description": "无任何参数，全部使用默认值"
        },
        {
            "name": "指定req_id",
            "url": "http://*************:9090/market-data/CFFEX/IF2509_Minute_15.txt#test_req_123",
            "expected_type": "2", 
            "expected_func_id": "3001",
            "expected_req_id": "test_req_123",
            "description": "指定req_id，func_id使用默认值"
        },
        {
            "name": "完整格式",
            "url": "http://*************:9090/market-data/CFFEX/IF2509_Minute_15.txt#test_req_456#3001",
            "expected_type": "2",
            "expected_func_id": "3001", 
            "expected_req_id": "test_req_456",
            "description": "完整参数格式"
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📈 测试 {i}: {test_case['name']}")
        print(f"   URL: {test_case['url']}")
        print(f"   说明: {test_case['description']}")
        
        try:
            # 运行分析器
            result = subprocess.run([
                sys.executable, 
                'analyzer_numpy_json.py', 
                test_case['url']
            ], capture_output=True, text=True, timeout=90)
            
            if result.returncode == 0:
                print(f"✅ 测试 {i} 执行成功")
                
                # 验证输出信息
                output = result.stdout
                
                # 检查type推断
                if "数据类型: 2 (15分钟K线)" in output:
                    print("  ✓ Type自动推断正确 (type=2)")
                else:
                    print("  ❌ Type推断失败")
                    continue
                
                # 检查功能编号
                if "功能编号: 3001" in output:
                    print("  ✓ 功能编号默认值正确 (func_id=3001)")
                else:
                    print("  ❌ 功能编号设置失败")
                    continue
                
                # 检查req_id处理
                if test_case.get('expected_req_id'):
                    if f"请求ID: {test_case['expected_req_id']}" in output:
                        print(f"  ✓ 请求ID设置正确 ({test_case['expected_req_id']})")
                    else:
                        print("  ❌ 请求ID设置失败")
                        continue
                else:
                    if "请求ID: 将从数据加载后获取" in output:
                        print("  ✓ 请求ID将从数据获取")
                    else:
                        print("  ❌ 请求ID处理异常")
                        continue
                
                # 检查数据限制
                if "数据量超过300条" in output or "15分钟线数据条数" in output:
                    print("  ✓ 数据加载和限制正常")
                else:
                    print("  ❌ 数据处理异常")
                    continue
                
                success_count += 1
                print(f"  🎉 测试 {i} 完全通过")
                
            else:
                print(f"❌ 测试 {i} 执行失败")
                print(f"返回码: {result.returncode}")
                if result.stderr:
                    print(f"错误信息: {result.stderr}")
                if result.stdout:
                    print(f"输出信息: {result.stdout}")
                    
        except subprocess.TimeoutExpired:
            print(f"❌ 测试 {i} 超时")
        except Exception as e:
            print(f"❌ 测试 {i} 异常: {str(e)}")
    
    return success_count, len(test_cases)

def verify_json_output():
    """验证JSON输出是否正确"""
    print("\n🔍 验证JSON输出")
    print("="*30)
    
    results_dir = os.path.join(os.path.dirname(__file__), 'analysis_results')
    if not os.path.exists(results_dir):
        print("❌ 结果目录不存在")
        return False
    
    files = os.listdir(results_dir)
    json_files = [f for f in files if f.endswith('_2.json')]
    
    if not json_files:
        print("❌ 未找到type=2的JSON文件")
        return False
    
    print(f"📊 找到 {len(json_files)} 个type=2的JSON文件")
    
    for filename in json_files:
        filepath = os.path.join(results_dir, filename)
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"\n📄 验证文件: {filename}")
            
            # 验证基本字段
            required_fields = ['func_id', 'req_id', 'type', 'error_id', 'data']
            for field in required_fields:
                if field in data:
                    print(f"  ✓ 包含字段: {field} = {data[field]}")
                else:
                    print(f"  ❌ 缺少字段: {field}")
                    return False
            
            # 验证type值
            if data.get('type') == '2':
                print("  ✓ type值正确 (15分钟K线)")
            else:
                print(f"  ❌ type值错误: {data.get('type')}")
                return False
            
            # 验证func_id
            if data.get('func_id') == '3001':
                print("  ✓ func_id正确")
            else:
                print(f"  ❌ func_id错误: {data.get('func_id')}")
                return False
            
            # 验证数据结构
            if 'data' in data and 'total_records' in data['data']:
                records = data['data']['total_records']
                if 'minute_15' in records:
                    count = records['minute_15']
                    print(f"  ✓ 15分钟数据条数: {count}")
                    if count <= 300:
                        print("  ✓ 数据量控制正确")
                    else:
                        print(f"  ⚠️ 数据量超限: {count}")
                else:
                    print("  ❌ 缺少minute_15记录")
                    return False
            
        except Exception as e:
            print(f"  ❌ 文件解析失败: {str(e)}")
            return False
    
    return True

def main():
    """主测试函数"""
    print("🎯 简化参数功能测试")
    print("📋 测试目标: 验证智能type推断和默认参数功能")
    print()
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"📂 当前目录: {current_dir}")
    
    # 检查必要文件
    if not os.path.exists('analyzer_numpy_json.py'):
        print("❌ 缺少analyzer_numpy_json.py文件")
        return False
    
    print("✅ 主分析器文件存在")
    
    # 运行参数测试
    success_count, total_count = test_simplified_params()
    
    # 验证JSON输出
    json_ok = verify_json_output()
    
    # 总结结果
    print("\n" + "="*60)
    print("🎉 测试总结")
    print(f"📊 参数测试: {success_count}/{total_count} 成功")
    print(f"🔍 JSON验证: {'✅ 通过' if json_ok else '❌ 失败'}")
    
    if success_count == total_count and json_ok:
        print("\n🎉 所有测试通过！简化参数功能正常")
        print("\n📝 功能特点总结:")
        print("  ✓ 智能type推断: 根据URL自动识别数据类型")
        print("  ✓ 默认func_id: 自动设置为3001")
        print("  ✓ 灵活req_id: 可选参数，支持从数据获取")
        print("  ✓ 参数简化: 支持最简URL格式")
        print("  ✓ 向下兼容: 支持完整参数格式")
        return True
    else:
        print("\n❌ 部分测试失败")
        print("🔧 请检查:")
        print("  1. URL解析逻辑是否正确")
        print("  2. 默认参数设置是否生效")
        print("  3. Type推断逻辑是否正确")
        print("  4. JSON输出格式是否正确")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
