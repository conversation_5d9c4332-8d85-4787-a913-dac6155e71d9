#!/usr/bin/env python3
"""
期货技术分析系统 v1.1 测试脚本
测试仅使用15分钟K线数据的分析功能
"""

import subprocess
import sys
import os

def test_v1_1_analysis():
    """测试v1.1版本的15分钟K线分析功能"""
    print("🚀 期货技术分析系统 v1.1 测试")
    print("📊 测试15分钟K线专用版本")
    print("="*60)
    
    # 测试URL - 包含type参数
    test_urls = [
        # 标准格式：包含type=2参数
        "http://117.72.196.97:9090/market-data/CFFEX/IF2509_Minute_15.txt#test_v1.1#3001#2",
        # 兼容格式：不包含type参数，默认为2
        "http://117.72.196.97:9090/market-data/CFFEX/IF2509_Minute_15.txt#test_v1.1_compat#3001"
    ]
    
    success_count = 0
    
    for i, test_url in enumerate(test_urls, 1):
        print(f"\n📈 测试 {i}: {test_url}")
        
        try:
            # 运行分析器
            result = subprocess.run([
                sys.executable, 
                'analyzer_numpy_json.py', 
                test_url
            ], capture_output=True, text=True, timeout=90)
            
            if result.returncode == 0:
                print(f"✅ 测试 {i} 成功！")
                success_count += 1
                
                # 检查输出信息
                if "15分钟K线" in result.stdout:
                    print("  ✓ 正确识别为15分钟K线数据")
                if "15分钟线数据条数" in result.stdout:
                    print("  ✓ 正确加载15分钟线数据")
                if "数据量超过300条" in result.stdout:
                    print("  ✓ 正确限制数据量到300条")
                
            else:
                print(f"❌ 测试 {i} 失败！")
                print(f"返回码: {result.returncode}")
                if result.stderr:
                    print(f"错误信息: {result.stderr}")
                if result.stdout:
                    print(f"输出信息: {result.stdout}")
                    
        except subprocess.TimeoutExpired:
            print(f"❌ 测试 {i} 超时！")
        except Exception as e:
            print(f"❌ 测试 {i} 异常: {str(e)}")
    
    return success_count, len(test_urls)

def check_output_files():
    """检查输出文件是否包含type标识"""
    print("\n📁 检查输出文件")
    print("="*30)
    
    results_dir = os.path.join(os.path.dirname(__file__), 'analysis_results')
    if not os.path.exists(results_dir):
        print("❌ 结果目录不存在")
        return False
    
    files = os.listdir(results_dir)
    json_files = [f for f in files if f.endswith('.json')]
    
    if not json_files:
        print("❌ 未找到JSON结果文件")
        return False
    
    print(f"📊 找到 {len(json_files)} 个JSON文件:")
    
    type_2_files = []
    for f in json_files:
        print(f"  - {f}")
        if f.endswith('_2.json'):
            type_2_files.append(f)
    
    if type_2_files:
        print(f"\n✅ 找到 {len(type_2_files)} 个包含type=2标识的文件:")
        for f in type_2_files:
            print(f"  ✓ {f}")
        return True
    else:
        print("\n⚠️ 未找到包含type=2标识的文件")
        return False

def validate_json_content():
    """验证JSON文件内容是否包含type字段"""
    print("\n🔍 验证JSON内容")
    print("="*30)
    
    results_dir = os.path.join(os.path.dirname(__file__), 'analysis_results')
    if not os.path.exists(results_dir):
        return False
    
    files = os.listdir(results_dir)
    json_files = [f for f in files if f.endswith('.json')]
    
    if not json_files:
        return False
    
    import json
    
    for filename in json_files:
        filepath = os.path.join(results_dir, filename)
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"📄 检查文件: {filename}")
            
            # 检查type字段
            if 'type' in data:
                print(f"  ✓ 包含type字段: {data['type']}")
                if data['type'] == '2':
                    print("  ✓ type值正确(15分钟K线)")
                else:
                    print(f"  ⚠️ type值异常: {data['type']}")
            else:
                print("  ❌ 缺少type字段")
            
            # 检查数据记录数
            if 'data' in data and 'total_records' in data['data']:
                records = data['data']['total_records']
                if 'minute_15' in records:
                    count = records['minute_15']
                    print(f"  ✓ 15分钟数据条数: {count}")
                    if count <= 300:
                        print("  ✓ 数据量控制正确")
                    else:
                        print(f"  ⚠️ 数据量超限: {count} > 300")
                        
        except Exception as e:
            print(f"  ❌ 文件解析失败: {str(e)}")
    
    return True

def main():
    """主测试函数"""
    print("🎯 期货技术分析系统 v1.1 完整测试")
    print("📋 测试目标: 验证15分钟K线专用版本功能")
    print()
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"📂 当前目录: {current_dir}")
    
    # 检查必要文件
    required_files = [
        'analyzer_numpy_json.py',
        'enhanced_analysis_engine.py',
        'numpy_technical_indicators.py',
        'remote_data_loader.py',
        'json_report_generator.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    print("✅ 所有必要文件都存在")
    
    # 运行分析测试
    success_count, total_count = test_v1_1_analysis()
    
    # 检查输出文件
    files_ok = check_output_files()
    
    # 验证JSON内容
    content_ok = validate_json_content()
    
    # 总结结果
    print("\n" + "="*60)
    print("🎉 测试总结")
    print(f"📊 分析测试: {success_count}/{total_count} 成功")
    print(f"📁 文件检查: {'✅ 通过' if files_ok else '❌ 失败'}")
    print(f"🔍 内容验证: {'✅ 通过' if content_ok else '❌ 失败'}")
    
    if success_count == total_count and files_ok and content_ok:
        print("\n🎉 所有测试通过！v1.1版本运行正常")
        print("\n📝 v1.1版本特点总结:")
        print("  ✓ 仅使用15分钟K线数据")
        print("  ✓ 数据量限制在300条以内")
        print("  ✓ 支持type参数(type=2)")
        print("  ✓ 文件名包含type标识")
        print("  ✓ JSON输出包含type字段")
        print("  ✓ 适合短期技术分析")
        return True
    else:
        print("\n❌ 部分测试失败")
        print("🔧 请检查:")
        print("  1. 网络连接是否正常")
        print("  2. 数据服务器是否可访问")
        print("  3. 15分钟K线数据是否可用")
        print("  4. type参数处理是否正确")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
