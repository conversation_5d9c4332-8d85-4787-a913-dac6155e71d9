﻿docker run -d --name logging-system -v $(pwd):/app -v /cffex/user/herh/work/oneapi-enhanced-complete-fixed-20250729-142227/logging-system/wheels:/wheels -v $(pwd)/logs:/-apps/logs -w /app --network=oneapi_network -p 3101:3001 -e MYSQL_HOST=Oneapi-mysql -e MYSQL_PORT=3306 -e MYSQL_USER=oneapi -e MYSQL_PASSWORD=123456 -e MYSQL_DB=one-api python:latest bash -c "python3 -m pip install --no-index --find-links=/wheels -r ./requirements.txt && python proxy-server.py"