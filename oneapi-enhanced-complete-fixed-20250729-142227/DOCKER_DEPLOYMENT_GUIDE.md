# OneAPI代理服务Docker部署指南（带登录功能）

## 概述

本指南专门针对您的生产环境Docker部署方式，提供了完整的登录功能集成方案。

## 部署环境要求

- Docker已安装
- 已存在`oneapi_network`网络
- OneAPI服务正在运行（容器名：one-api）
- MySQL服务正在运行（容器名：Oneapi-mysql）

## 快速部署

### 方法1：使用快速部署脚本（推荐）

```bash
cd oneapi-enhanced-complete-fixed-20250729-142227
./quick_deploy.sh
```

### 方法2：使用您原有的命令格式

```bash
# 停止现有容器
docker stop logging-system 2>/dev/null || true
docker rm logging-system 2>/dev/null || true

# 创建配置目录
mkdir -p $(pwd)/config

# 启动带登录功能的容器
docker run -d \
  --name logging-system \
  -v $(pwd)/logging-system:/app \
  -v $(pwd)/logging-system/wheels:/wheels \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/config:/app/config \
  -w /app \
  --network=oneapi_network \
  -p 3101:3001 \
  -e MYSQL_HOST=Oneapi-mysql \
  -e MYSQL_PORT=3306 \
  -e MYSQL_USER=oneapi \
  -e MYSQL_PASSWORD=123456 \
  -e MYSQL_DB=one-api \
  -e ADMIN_USERNAME=admin \
  -e ADMIN_PASSWORD=admin123 \
  -e SESSION_SECRET=your-secret-key-here \
  -e CONFIG_FILE=/app/config/login_config.json \
  -e ONEAPI_BASE_URL=http://one-api:3000 \
  python:3.11-slim \
  bash -c "apt-get update && apt-get install -y curl && python3 -m pip install --no-index --find-links=/wheels -r ./requirements.txt && python proxy-server.py"
```

## 新增功能特性

### 🔐 登录认证系统
- **Session管理**: 基于FastAPI SessionMiddleware
- **持久化配置**: 配置文件保存在`/app/config/login_config.json`
- **运行时修改**: 支持通过Web界面修改登录凭据
- **安全保护**: 所有敏感API端点都需要登录

### 🎨 Web界面增强
- **登录页面**: 美观的登录界面
- **设置功能**: 在线修改用户名和密码
- **登出功能**: 安全退出登录
- **状态显示**: 实时显示登录状态

### 📁 文件持久化
- **配置文件**: `/app/config/login_config.json`
- **日志文件**: `/app/logs/conversation_logs.jsonl`
- **容器重启**: 配置和数据保持不变

## 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `ADMIN_USERNAME` | admin | 管理员用户名 |
| `ADMIN_PASSWORD` | admin123 | 管理员密码 |
| `SESSION_SECRET` | 自动生成 | Session加密密钥 |
| `CONFIG_FILE` | /app/config/login_config.json | 配置文件路径 |
| `ONEAPI_BASE_URL` | http://one-api:3000 | OneAPI服务地址 |
| `MYSQL_HOST` | Oneapi-mysql | MySQL主机名 |
| `MYSQL_USER` | oneapi | MySQL用户名 |
| `MYSQL_PASSWORD` | 123456 | MySQL密码 |
| `MYSQL_DB` | one-api | MySQL数据库名 |

## 使用说明

### 首次访问
1. 访问 `http://localhost:3101`
2. 使用默认凭据登录：
   - 用户名：admin
   - 密码：admin123

### 修改登录凭据
1. 登录后点击"⚙️ 设置"按钮
2. 输入新的用户名和密码
3. 输入当前密码确认
4. 点击"更新配置"

### 安全退出
- 点击"🚪 登出"按钮安全退出

## 目录结构

```
oneapi-enhanced-complete-fixed-20250729-142227/
├── logging-system/           # 代理服务代码
│   ├── proxy-server.py      # 主程序（已添加登录功能）
│   ├── requirements.txt     # Python依赖
│   └── wheels/              # 离线安装包
├── config/                  # 配置文件目录（持久化）
│   └── login_config.json    # 登录配置文件
├── logs/                    # 日志文件目录（持久化）
│   └── conversation_logs.jsonl
├── quick_deploy.sh          # 快速部署脚本
├── deploy_docker_login.sh   # 完整部署脚本
└── restart_logging.sh       # 重启脚本（部署后生成）
```

## 常用操作

### 查看服务状态
```bash
docker ps | grep logging-system
curl http://localhost:3101/health
```

### 查看日志
```bash
# 查看容器日志
docker logs logging-system

# 查看应用日志
tail -f logs/conversation_logs.jsonl
```

### 重启服务
```bash
# 使用生成的脚本
./restart_logging.sh

# 或手动重启
docker restart logging-system
```

### 停止服务
```bash
# 使用生成的脚本
./stop_logging.sh

# 或手动停止
docker stop logging-system
```

### 进入容器调试
```bash
docker exec -it logging-system bash
```

## 安全建议

### 生产环境配置
1. **修改默认密码**: 首次部署后立即修改默认凭据
2. **使用强密码**: 密码长度至少8位，包含字母、数字、特殊字符
3. **定期更新**: 定期更换登录密码
4. **备份配置**: 定期备份`config/`和`logs/`目录

### 网络安全
1. **防火墙**: 限制3101端口的访问来源
2. **HTTPS**: 生产环境建议使用反向代理配置HTTPS
3. **VPN**: 考虑通过VPN访问管理界面

## 故障排除

### 容器启动失败
```bash
# 查看详细错误
docker logs logging-system

# 检查网络连接
docker network ls | grep oneapi_network

# 检查依赖服务
docker ps | grep -E "(one-api|Oneapi-mysql)"
```

### 登录问题
```bash
# 检查配置文件
cat config/login_config.json

# 重置配置（删除配置文件，使用环境变量）
rm config/login_config.json
docker restart logging-system
```

### 权限问题
```bash
# 检查目录权限
ls -la config/ logs/

# 修复权限
chmod 755 config/ logs/
```

## 升级说明

### 从旧版本升级
1. 停止现有容器
2. 备份配置和日志
3. 使用新的部署脚本重新部署
4. 配置文件会自动迁移

### 配置迁移
- 旧版本的环境变量配置会自动转换为配置文件
- 首次启动时会创建默认配置文件
- 支持热更新，无需重启容器

## 技术支持

如遇到问题，请检查：
1. Docker容器状态和日志
2. 网络连接和端口占用
3. 配置文件格式和权限
4. 依赖服务（OneAPI、MySQL）状态

通过以上优化，您的OneAPI代理服务现在具备了完整的登录功能，同时保持了原有的部署方式和配置习惯。
