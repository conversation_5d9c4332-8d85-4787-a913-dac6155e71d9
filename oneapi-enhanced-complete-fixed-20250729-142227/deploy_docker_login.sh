#!/bin/bash

# OneAPI代理服务Docker部署脚本（带登录功能）
# 适用于生产环境的单容器部署方式

set -e

echo "🚀 OneAPI代理服务Docker部署脚本（带登录功能）"
echo "================================================"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 设置工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "📁 当前工作目录: $SCRIPT_DIR"

# 检查必要文件是否存在
if [ ! -f "logging-system/proxy-server.py" ]; then
    echo "❌ 找不到proxy-server.py文件"
    exit 1
fi

if [ ! -f "logging-system/requirements.txt" ]; then
    echo "❌ 找不到requirements.txt文件"
    exit 1
fi

# 设置默认配置
DEFAULT_CONTAINER_NAME="logging-system"
DEFAULT_PORT="3101"
DEFAULT_ONEAPI_HOST="one-api"
DEFAULT_ONEAPI_PORT="3000"
DEFAULT_MYSQL_HOST="Oneapi-mysql"
DEFAULT_MYSQL_PORT="3306"
DEFAULT_MYSQL_USER="oneapi"
DEFAULT_MYSQL_PASSWORD="123456"
DEFAULT_MYSQL_DB="one-api"
DEFAULT_NETWORK="oneapi_network"

# 询问用户配置
echo ""
echo "🔧 部署配置"
read -p "容器名称 [$DEFAULT_CONTAINER_NAME]: " CONTAINER_NAME
CONTAINER_NAME=${CONTAINER_NAME:-$DEFAULT_CONTAINER_NAME}

read -p "代理服务端口 [$DEFAULT_PORT]: " PROXY_PORT
PROXY_PORT=${PROXY_PORT:-$DEFAULT_PORT}

read -p "OneAPI服务地址 [$DEFAULT_ONEAPI_HOST]: " ONEAPI_HOST
ONEAPI_HOST=${ONEAPI_HOST:-$DEFAULT_ONEAPI_HOST}

read -p "OneAPI服务端口 [$DEFAULT_ONEAPI_PORT]: " ONEAPI_PORT
ONEAPI_PORT=${ONEAPI_PORT:-$DEFAULT_ONEAPI_PORT}

read -p "MySQL主机 [$DEFAULT_MYSQL_HOST]: " MYSQL_HOST
MYSQL_HOST=${MYSQL_HOST:-$DEFAULT_MYSQL_HOST}

read -p "MySQL端口 [$DEFAULT_MYSQL_PORT]: " MYSQL_PORT
MYSQL_PORT=${MYSQL_PORT:-$DEFAULT_MYSQL_PORT}

read -p "MySQL用户名 [$DEFAULT_MYSQL_USER]: " MYSQL_USER
MYSQL_USER=${MYSQL_USER:-$DEFAULT_MYSQL_USER}

read -p "MySQL密码 [$DEFAULT_MYSQL_PASSWORD]: " MYSQL_PASSWORD
MYSQL_PASSWORD=${MYSQL_PASSWORD:-$DEFAULT_MYSQL_PASSWORD}

read -p "MySQL数据库 [$DEFAULT_MYSQL_DB]: " MYSQL_DB
MYSQL_DB=${MYSQL_DB:-$DEFAULT_MYSQL_DB}

read -p "Docker网络 [$DEFAULT_NETWORK]: " NETWORK
NETWORK=${NETWORK:-$DEFAULT_NETWORK}

# 登录凭据配置
echo ""
echo "🔐 登录凭据配置"
read -p "管理员用户名 [admin]: " ADMIN_USERNAME
ADMIN_USERNAME=${ADMIN_USERNAME:-admin}

read -s -p "管理员密码 [admin123]: " ADMIN_PASSWORD
echo ""
ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin123}

read -p "Session密钥 (留空自动生成): " SESSION_SECRET
if [ -z "$SESSION_SECRET" ]; then
    SESSION_SECRET=$(openssl rand -hex 32 2>/dev/null || echo "oneapi-proxy-session-$(date +%s)-$(shuf -i 1000-9999 -n 1)")
    echo "🔑 已生成Session密钥"
fi

# 停止并删除现有容器
echo ""
echo "🛑 停止现有容器..."
docker stop "$CONTAINER_NAME" 2>/dev/null || true
docker rm "$CONTAINER_NAME" 2>/dev/null || true

# 确保网络存在
echo ""
echo "🌐 检查Docker网络..."
if ! docker network ls | grep -q "$NETWORK"; then
    echo "创建Docker网络: $NETWORK"
    docker network create "$NETWORK"
else
    echo "Docker网络已存在: $NETWORK"
fi

# 确保日志目录存在
echo ""
echo "📁 准备目录..."
mkdir -p "$(pwd)/logs"
mkdir -p "$(pwd)/config"

# 构建Docker运行命令
DOCKER_CMD="docker run -d \
  --name $CONTAINER_NAME \
  -v $(pwd)/logging-system:/app \
  -v $(pwd)/logging-system/wheels:/wheels \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/config:/app/config \
  -w /app \
  --network=$NETWORK \
  -p $PROXY_PORT:3001 \
  -e ONEAPI_BASE_URL=http://$ONEAPI_HOST:$ONEAPI_PORT \
  -e MYSQL_HOST=$MYSQL_HOST \
  -e MYSQL_PORT=$MYSQL_PORT \
  -e MYSQL_USER=$MYSQL_USER \
  -e MYSQL_PASSWORD=$MYSQL_PASSWORD \
  -e MYSQL_DB=$MYSQL_DB \
  -e ADMIN_USERNAME=$ADMIN_USERNAME \
  -e ADMIN_PASSWORD=$ADMIN_PASSWORD \
  -e SESSION_SECRET=$SESSION_SECRET \
  -e CONFIG_FILE=/app/config/login_config.json \
  -e LOG_DIR=/app/logs \
  python:3.11-slim \
  bash -c 'apt-get update && apt-get install -y curl && python3 -m pip install --no-index --find-links=/wheels -r ./requirements.txt && python proxy-server.py'"

# 启动容器
echo ""
echo "🚀 启动代理服务容器..."
echo "执行命令: $DOCKER_CMD"
eval $DOCKER_CMD

# 等待服务启动
echo ""
echo "⏳ 等待服务启动..."
sleep 15

# 检查容器状态
echo ""
echo "🔍 检查容器状态..."
if docker ps | grep -q "$CONTAINER_NAME"; then
    echo "✅ 容器启动成功"
    docker ps | grep "$CONTAINER_NAME"
else
    echo "❌ 容器启动失败"
    echo "查看容器日志:"
    docker logs "$CONTAINER_NAME"
    exit 1
fi

# 测试服务连接
echo ""
echo "🧪 测试服务连接..."
max_attempts=10
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -s "http://localhost:$PROXY_PORT/health" > /dev/null; then
        echo "✅ 代理服务启动成功"
        break
    else
        echo "⏳ 等待代理服务启动... (尝试 $attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ 代理服务启动失败，请检查日志"
    docker logs "$CONTAINER_NAME"
    exit 1
fi

# 保存部署信息
cat > deploy_info.txt << EOF
OneAPI代理服务部署信息
=====================
部署时间: $(date)
容器名称: $CONTAINER_NAME
服务端口: $PROXY_PORT
OneAPI地址: http://$ONEAPI_HOST:$ONEAPI_PORT
MySQL配置: $MYSQL_USER@$MYSQL_HOST:$MYSQL_PORT/$MYSQL_DB
管理员用户: $ADMIN_USERNAME
网络: $NETWORK

访问地址: http://localhost:$PROXY_PORT
配置文件: $(pwd)/config/login_config.json
日志目录: $(pwd)/logs

常用命令:
- 查看日志: docker logs $CONTAINER_NAME
- 停止服务: docker stop $CONTAINER_NAME
- 重启服务: docker restart $CONTAINER_NAME
- 删除容器: docker rm -f $CONTAINER_NAME
EOF

# 显示部署信息
echo ""
echo "🎉 部署完成！"
echo "=================================="
echo "服务访问信息:"
echo "  代理服务: http://localhost:$PROXY_PORT"
echo "  OneAPI服务: http://$ONEAPI_HOST:$ONEAPI_PORT"
echo ""
echo "登录信息:"
echo "  用户名: $ADMIN_USERNAME"
echo "  密码: [已设置]"
echo ""
echo "配置文件:"
echo "  登录配置: $(pwd)/config/login_config.json"
echo "  日志目录: $(pwd)/logs"
echo "  部署信息: $(pwd)/deploy_info.txt"
echo ""
echo "⚠️  重要提醒:"
echo "  1. 配置文件已持久化，重启容器后配置保持不变"
echo "  2. 可通过Web界面的设置功能修改登录凭据"
echo "  3. 生产环境建议使用HTTPS协议"
echo "  4. 定期备份配置和日志文件"
echo ""
echo "📋 常用命令:"
echo "  查看日志: docker logs $CONTAINER_NAME"
echo "  停止服务: docker stop $CONTAINER_NAME"
echo "  重启服务: docker restart $CONTAINER_NAME"
echo ""
echo "✅ 部署完成，请访问 http://localhost:$PROXY_PORT 开始使用！"
