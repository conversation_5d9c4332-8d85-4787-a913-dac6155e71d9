#!/usr/bin/env python3
"""
OneAPI代理服务 - 简化版
功能：代理OneAPI请求，记录用户对话，提供Web界面
"""

import os
import json
import time
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, Optional

import httpx
import uvicorn
from fastapi import FastAPI, Request, HTTPException, Depends, Form
from fastapi.responses import JSONResponse, HTMLResponse, Response, RedirectResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.sessions import SessionMiddleware

from starlette.responses import StreamingResponse

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入数据库用户解析器
try:
    from db_user_resolver import get_user_info_from_database, test_database_connection
    DATABASE_AVAILABLE = True
    logger.info("✅ 数据库用户解析器已加载")
except ImportError as e:
    DATABASE_AVAILABLE = False
    logger.warning(f"⚠️ 数据库用户解析器不可用: {e}")

# 配置
ONEAPI_BASE_URL = os.getenv("ONEAPI_BASE_URL", "http://one-api:3000")
PROXY_PORT = int(os.getenv("PROXY_PORT", "3001"))
LOG_DIR = os.getenv("LOG_DIR", "/app/logs")
LOG_FILE = os.getenv("LOG_FILE", os.path.join(LOG_DIR, "conversation_logs.jsonl"))

MYSQL_HOST = os.getenv("MYSQL_HOST", "Oneapi-mysql")
MYSQL_PORT = int(os.getenv("MYSQL_PORT", "3306"))
MYSQL_USER = os.getenv("MYSQL_USER", "oneapi")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD", "123456")
MYSQL_DB = os.getenv("MYSQL_DB", "one-api")

# 登录配置 - 针对生产环境优化
ADMIN_USERNAME = os.getenv("ADMIN_USERNAME", "admin")
ADMIN_PASSWORD = os.getenv("ADMIN_PASSWORD", "admin123")
SESSION_SECRET = os.getenv("SESSION_SECRET", "oneapi-proxy-session-secret-change-in-production")

# 登录配置文件路径（支持持久化配置）
CONFIG_FILE = os.getenv("CONFIG_FILE", "/app/login_config.json")

os.makedirs(LOG_DIR,exist_ok=True)




import re

def _flatten_content_to_text(content):
    """
    支持 OpenAI 风格 content: str | list[{'type':'text','text':'...'}, {...}]
    统一拉平成纯文本。
    """
    if isinstance(content, list):
        parts = []
        for it in content:
            if isinstance(it, dict) and it.get("type") == "text":
                parts.append(it.get("text") or "")
            elif isinstance(it, str):
                parts.append(it)
        return "".join(parts)
    return str(content or "")

def _extract_last_user_segment(text: str) -> str:
    """
    从 Continue 风格提示里，仅取“最后一个 <im_start>user ... <im_end>”内容。
    失败则返回原文。
    """
    if not text:
        return ""
    # 1) 严格匹配 Continue 样式
    user_blocks = re.findall(r"<im_start>\s*user\s*>(.*?)<im_end>", text, flags=re.S | re.I)
    if user_blocks:
        seg = user_blocks[-1].strip()
        # 去掉当轮片段里可能夹带的 assistant 残留
        seg = re.sub(r"<im_start>\s*assistant\s*>.*?<im_end>", "", seg, flags=re.S | re.I).strip()
        return seg
    # 2) 退路：如果没有标记，尽量截取最后一次明显的“用户段落”
    #    例如某些模板会拼 "user:" / "assistant:" 作为分隔符
    m = re.search(r"(?:^|\n)\s*user\s*:\s*(.*)$", text, flags=re.S | re.I)
    if m:
        return m.group(1).strip()
    return text.strip()

# ===== Helpers for current turn & tokens =====
def _last_user_message(messages):
    """
    取最后一条 role=='user'，并将其 content 清洗为“当轮内容”。
    """
    if not isinstance(messages, list):
        return None
    for m in reversed(messages):
        if isinstance(m, dict) and m.get("role") == "user":
            raw = m.get("content")
            text = _flatten_content_to_text(raw)
            clean = _extract_last_user_segment(text)
            return {"role": "user", "content": clean}
    return None

def _assistant_message_from_response(response_data):
    try:
        ch0 = (response_data or {}).get("choices", [{}])[0]
        msg = ch0.get("message", {}) or {}
        return {"role": "assistant", "content": (msg.get("content") or "")}
    except Exception:
        return {"role": "assistant", "content": ""}

def _rough_token_count(text):
    # 非精确：大约 4 字符 ≈ 1 token，兜底保证 >0
    if not text:
        return 1
    return max(1, (len(text) + 3) // 4)

def _ensure_usage(model, request_data, response_data):
    """优先取上游 usage；没有就估算（只用当前轮 user/assistant 文本）"""
    usage = (response_data or {}).get("usage")
    if usage:
        return usage
    user_msg = _last_user_message((request_data or {}).get("messages", []))
    asst_msg = _assistant_message_from_response(response_data)
    pt = _rough_token_count(user_msg.get("content") if user_msg else "")
    ct = _rough_token_count(asst_msg.get("content") if asst_msg else "")
    return {"prompt_tokens": pt, "completion_tokens": ct, "total_tokens": pt + ct}




# 初始化FastAPI
app = FastAPI(
    title="OneAPI代理服务",
    description="代理OneAPI请求并记录用户对话",
    version="1.0.0"
)

# 添加Session中间件
app.add_middleware(SessionMiddleware, secret_key=SESSION_SECRET)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 用户信息缓存
user_cache: Dict[str, Dict] = {}

# 加载用户映射文件
def load_user_mapping():
    """加载用户映射文件"""
    try:
        if os.path.exists("user-mapping.json"):
            with open("user-mapping.json", "r", encoding="utf-8") as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"加载用户映射文件失败: {e}")
    return {}

# 全局用户映射
user_mapping = load_user_mapping()

# 登录配置管理
def load_login_config():
    """加载登录配置"""
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, "r", encoding="utf-8") as f:
                config = json.load(f)
                return config
    except Exception as e:
        logger.error(f"加载登录配置失败: {e}")

    # 返回默认配置
    return {
        "username": ADMIN_USERNAME,
        "password": ADMIN_PASSWORD,
        "session_secret": SESSION_SECRET,
        "created_at": datetime.now().isoformat(),
        "last_updated": datetime.now().isoformat()
    }

def save_login_config(config):
    """保存登录配置"""
    try:
        config["last_updated"] = datetime.now().isoformat()
        os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)
        with open(CONFIG_FILE, "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        logger.info("登录配置已保存")
        return True
    except Exception as e:
        logger.error(f"保存登录配置失败: {e}")
        return False

# 加载当前登录配置
login_config = load_login_config()

# 认证相关函数
def check_login(request: Request) -> bool:
    """检查用户是否已登录"""
    return request.session.get("logged_in", False)

def require_login(request: Request):
    """要求用户登录的依赖函数"""
    if not check_login(request):
        raise HTTPException(status_code=401, detail="需要登录")
    return True

def verify_credentials(username: str, password: str) -> bool:
    """验证登录凭据"""
    current_config = load_login_config()
    return (username == current_config.get("username") and
            password == current_config.get("password"))

def get_login_page():
    """返回登录页面HTML"""
    return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OneAPI代理服务 - 登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .login-header p {
            color: #666;
            margin: 0;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        .login-btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .login-btn:hover {
            background: #5a6fd8;
        }
        .error {
            color: #dc3545;
            text-align: center;
            margin-top: 15px;
        }
        .success {
            color: #28a745;
            text-align: center;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🤖 OneAPI代理服务</h1>
            <p>请登录以访问对话记录</p>
        </div>

        <form id="loginForm" method="post" action="/login">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" class="login-btn">登录</button>
        </form>

        <div id="message"></div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const messageDiv = document.getElementById('message');

            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    messageDiv.innerHTML = '<p class="success">登录成功，正在跳转...</p>';
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    const result = await response.json();
                    messageDiv.innerHTML = `<p class="error">${result.detail || '登录失败'}</p>`;
                }
            } catch (error) {
                messageDiv.innerHTML = '<p class="error">网络错误，请重试</p>';
            }
        });
    </script>
</body>
</html>
    """

def get_web_interface():
    """返回Web界面HTML"""
    return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OneAPI代理服务 - 对话记录</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status { display: flex; gap: 20px; margin-bottom: 20px; }
        .status-card { flex: 1; padding: 15px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #007bff; }
        .logs { margin-top: 20px; }
        .log-entry { background: #f8f9fa; margin: 10px 0; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745; }
        .log-meta { color: #666; font-size: 0.9em; margin-bottom: 10px; }
        .log-content { margin: 10px 0; }
        .user-msg { background: #e3f2fd; padding: 8px; margin: 5px 0; border-radius: 4px; }
        .assistant-msg { background: #f3e5f5; padding: 8px; margin: 5px 0; border-radius: 4px; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .error { color: #dc3545; }
        .success { color: #28a745; }

        /* 模态框样式 */
        .modal { position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); }
        .modal-content { background-color: white; margin: 10% auto; padding: 0; border-radius: 8px; width: 90%; max-width: 500px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .modal-header { padding: 20px; border-bottom: 1px solid #ddd; display: flex; justify-content: space-between; align-items: center; }
        .modal-header h3 { margin: 0; }
        .modal-body { padding: 20px; }
        .close { color: #aaa; font-size: 28px; font-weight: bold; cursor: pointer; }
        .close:hover { color: #000; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 OneAPI代理服务</h1>
            <p>实时监控用户对话记录</p>
        </div>
        
        <div class="status" id="status">
            <div class="status-card">
                <h3>服务状态</h3>
                <p id="proxy-status">检查中...</p>
            </div>
            <div class="status-card">
                <h3>OneAPI连接</h3>
                <p id="oneapi-status">检查中...</p>
            </div>
            <div class="status-card">
                <h3>对话记录</h3>
                <p id="log-count">加载中...</p>
            </div>
        </div>
        
        <div>
            <button class="btn" onclick="refreshLogs()">🔄 刷新日志</button>
            <button class="btn" onclick="clearLogs()">🗑️ 清空日志</button>
            <button class="btn" onclick="downloadLogs()">📥 下载日志</button>
            <button class="btn" onclick="showConfigModal()" style="background: #6c757d;">⚙️ 设置</button>
            <button class="btn" onclick="logout()" style="background: #dc3545;">🚪 登出</button>
        </div>
        
        <div class="logs">
            <h3>📝 最近对话记录</h3>
            <div id="logs-container">加载中...</div>
        </div>
    </div>

    <!-- 配置模态框 -->
    <div id="configModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>⚙️ 系统设置</h3>
                <span class="close" onclick="closeConfigModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="configForm">
                    <div class="form-group">
                        <label for="newUsername">新用户名:</label>
                        <input type="text" id="newUsername" name="newUsername" required>
                    </div>
                    <div class="form-group">
                        <label for="newPassword">新密码:</label>
                        <input type="password" id="newPassword" name="newPassword" required minlength="6">
                    </div>
                    <div class="form-group">
                        <label for="currentPassword">当前密码:</label>
                        <input type="password" id="currentPassword" name="currentPassword" required>
                    </div>
                    <button type="submit" class="btn">更新配置</button>
                </form>
                <div id="configMessage"></div>
            </div>
        </div>
    </div>
<script>
  async function fetchJSON(url) {
    const res = await fetch(url, { cache: 'no-store' });
    if (!res.ok) {
      const text = await res.text();
      throw new Error(`HTTP ${res.status} ${res.statusText} | body: ${text.slice(0,200)}`);
    }
    // 某些异常情况下返回空字符串，这里手动 parse 以拿到更清晰的错误
    const text = await res.text();
    try {
      return JSON.parse(text);
    } catch (e) {
      throw new Error(`Invalid JSON: ${e.message} | body: ${text.slice(0,200)}`);
    }
  }

  async function checkStatus() {
    const el = document.getElementById('proxy-status');
    const one = document.getElementById('oneapi-status');
    const cnt = document.getElementById('log-count');
    try {
      const data = await fetchJSON('/api/status');
      el.innerHTML = `<span class="success">✅ 运行正常</span>`;
      one.innerHTML = data.oneapi_connected
        ? `<span class="success">✅ 连接正常</span>`
        : `<span class="error">❌ 连接失败</span>`;
      cnt.innerHTML = `缓存用户: ${data.cached_users}`;
    } catch (err) {
      el.innerHTML = `<span class="error">❌ 服务异常: ${String(err).replace(/[<>&]/g,'')}</span>`;
    }
  }

  async function loadLogs() {
    const container = document.getElementById('logs-container');
    try {
      const data = await fetchJSON('/logs?limit=10');
      if (data.logs && data.logs.length > 0) {
        container.innerHTML = data.logs.map(log => `
          <div class="log-entry">
            <div class="log-meta">
              🕒 ${new Date(log.timestamp).toLocaleString()} |
              👤 ${(log.user_info && log.user_info.username) ? log.user_info.username : '未知用户'} |
              🔑 ${log.metadata?.api_key || log.metadata?.api_key_hash || '未知Key'} |
              🤖 ${log.request?.model || '未知模型'} |
              ⏱️ ${log.metadata?.duration_ms ?? '-'}ms
            </div>
            <div class="log-content">
              ${(log.request?.messages || []).map(msg => `
                <div class="${msg.role === 'user' ? 'user-msg' : 'assistant-msg'}">
                  <strong>${msg.role}:</strong> ${msg.content ?? ''}
                </div>
              `).join('')}
              ${(log.response?.choices || []).map(choice => `
                <div class="assistant-msg">
                  <strong>assistant:</strong> ${choice.message?.content ?? ''}
                </div>
              `).join('')}
            </div>
            <div class="log-meta">
              📊 Tokens: ${log.response?.usage
                ? `输入${log.response.usage.prompt_tokens ?? 0} + 输出${log.response.usage.completion_tokens ?? 0} = 总计${log.response.usage.total_tokens ?? 0}`
                : '未知'}
            </div>
          </div>
        `).join('');
      } else {
        container.innerHTML = '<p>暂无对话记录</p>';
      }
    } catch (err) {
      container.innerHTML = `<p class="error">加载日志失败：${String(err).replace(/[<>&]/g,'')}</p>`;
    }
  }

  function refreshLogs() {
    checkStatus();
    loadLogs();
  }

  async function logout() {
    if (confirm('确定要登出吗？')) {
      try {
        const response = await fetch('/logout', { method: 'POST' });
        if (response.ok) {
          window.location.href = '/login';
        } else {
          alert('登出失败');
        }
      } catch (error) {
        alert('网络错误');
      }
    }
  }

  function showConfigModal() {
    document.getElementById('configModal').style.display = 'block';
    loadCurrentConfig();
  }

  function closeConfigModal() {
    document.getElementById('configModal').style.display = 'none';
    document.getElementById('configMessage').innerHTML = '';
  }

  async function loadCurrentConfig() {
    try {
      const response = await fetch('/api/config');
      if (response.ok) {
        const config = await response.json();
        document.getElementById('newUsername').value = config.username || '';
      }
    } catch (error) {
      console.error('加载配置失败:', error);
    }
  }

  // 配置表单提交
  document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('configForm').addEventListener('submit', async function(e) {
      e.preventDefault();

      const formData = new FormData(this);
      const messageDiv = document.getElementById('configMessage');

      try {
        const response = await fetch('/api/config', {
          method: 'POST',
          body: formData
        });

        if (response.ok) {
          messageDiv.innerHTML = '<p class="success">配置更新成功！</p>';
          setTimeout(() => {
            closeConfigModal();
            // 可选：刷新页面以应用新配置
            // window.location.reload();
          }, 2000);
        } else {
          const result = await response.json();
          messageDiv.innerHTML = `<p class="error">${result.detail || '更新失败'}</p>`;
        }
      } catch (error) {
        messageDiv.innerHTML = '<p class="error">网络错误，请重试</p>';
      }
    });
  });

  // 初始加载 & 自动刷新
  checkStatus();
  loadLogs();
  setInterval(refreshLogs, 30000);
</script>


</body>
</html>
    """

async def get_user_info_from_token(api_key: str) -> Optional[Dict]:
    """从OneAPI获取用户信息 - 生产环境版本"""
    if api_key in user_cache:
        return user_cache[api_key]

    # 优先尝试数据库直连方案
    if DATABASE_AVAILABLE:
        try:
            logger.info(f"🔍 尝试从数据库获取用户信息...")
            user_info = await get_user_info_from_database(api_key)
            if user_info:
                user_cache[api_key] = user_info
                logger.info(f"✅ 从数据库获取用户信息成功: {user_info.get('username', 'unknown')}")
                return user_info
            else:
                logger.info(f"⚠️ 数据库中未找到API Key对应的用户")
        except Exception as e:
            logger.error(f"❌ 数据库查询失败: {e}")

    # 备选方案：API查询
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {api_key}"}

            # 方法1: 尝试直接获取当前用户信息
            logger.info(f"尝试获取用户信息...")
            response = await client.get(f"{ONEAPI_BASE_URL}/api/user/self", headers=headers)
            logger.info(f"用户信息API响应: {response.status_code}")

            if response.status_code == 200:
                user_info = response.json()
                logger.info(f"用户信息响应内容: {user_info}")
                if user_info.get("success"):
                    user_data = user_info.get("data", {})
                    user_cache[api_key] = user_data
                    logger.info(f"✅ 从OneAPI获取用户信息: {user_data.get('username', 'unknown')}")
                    return user_data

            # 方法2: 尝试通过不同的API端点获取token信息
            endpoints_to_try = [
                "/api/token/self",
                "/api/token",
                "/api/tokens",
                "/api/user/token"
            ]

            for endpoint in endpoints_to_try:
                logger.info(f"尝试端点: {endpoint}")
                try:
                    response = await client.get(f"{ONEAPI_BASE_URL}{endpoint}", headers=headers)
                    logger.info(f"端点 {endpoint} 响应: {response.status_code}")

                    if response.status_code == 200:
                        token_info = response.json()
                        logger.info(f"Token信息: {token_info}")

                        if token_info.get("success"):
                            # 处理不同的响应格式
                            token_data = token_info.get("data", {})

                            # 如果data是列表，取第一个
                            if isinstance(token_data, list) and len(token_data) > 0:
                                token_data = token_data[0]

                            user_id = token_data.get("user_id")
                            if user_id:
                                # 尝试通过user_id获取用户详细信息
                                user_detail = await get_user_detail_by_id(client, user_id, api_key)
                                if user_detail:
                                    user_cache[api_key] = user_detail
                                    return user_detail

                                # 如果无法获取详细信息，构造基本信息
                                user_data = {
                                    "id": user_id,
                                    "username": f"user_{user_id}",
                                    "email": f"user_{user_id}@oneapi.local",
                                    "token_name": token_data.get("name", "unknown"),
                                    "token_id": token_data.get("id"),
                                    "source": "token_api"
                                }
                                user_cache[api_key] = user_data
                                logger.info(f"✅ 通过token API获取用户信息: user_{user_id}")
                                return user_data

                except Exception as e:
                    logger.info(f"端点 {endpoint} 失败: {e}")
                    continue

            # 方法3: 尝试使用管理员权限查询（如果有的话）
            admin_user_info = await try_admin_user_lookup(client, api_key)
            if admin_user_info:
                user_cache[api_key] = admin_user_info
                return admin_user_info

    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")

    # 最后的备选方案：创建基于API Key的用户信息
    logger.info(f"⚠️ 无法获取用户信息，创建基于API Key的标识")
    user_data = {
        "id": f"api_{api_key[:8]}",
        "username": f"user_{api_key[:8]}",
        "email": f"user_{api_key[:8]}@oneapi.local",
        "api_key_prefix": api_key[:8],
        "source": "generated",
        "note": "无法从OneAPI获取用户信息，基于API Key生成"
    }
    user_cache[api_key] = user_data
    return user_data

async def get_user_detail_by_id(client: httpx.AsyncClient, user_id: str, api_key: str) -> Optional[Dict]:
    """通过用户ID获取用户详细信息"""
    try:
        # 尝试不同的用户详情API端点
        user_endpoints = [
            f"/api/user/{user_id}",
            f"/api/users/{user_id}",
            f"/api/user/detail/{user_id}"
        ]

        headers = {"Authorization": f"Bearer {api_key}"}

        for endpoint in user_endpoints:
            try:
                response = await client.get(f"{ONEAPI_BASE_URL}{endpoint}", headers=headers)
                if response.status_code == 200:
                    user_info = response.json()
                    if user_info.get("success"):
                        user_data = user_info.get("data", {})
                        logger.info(f"✅ 获取到用户详细信息: {user_data.get('username', 'unknown')}")
                        return user_data
            except Exception as e:
                logger.info(f"用户详情端点 {endpoint} 失败: {e}")
                continue

    except Exception as e:
        logger.error(f"获取用户详细信息失败: {e}")

    return None

async def try_admin_user_lookup(client: httpx.AsyncClient, api_key: str) -> Optional[Dict]:
    """尝试使用管理员权限查找用户信息"""
    try:
        # 如果当前token有管理员权限，尝试查询所有token来找到对应的用户
        headers = {"Authorization": f"Bearer {api_key}"}

        admin_endpoints = [
            "/api/token?p=0&size=1000",
            "/api/tokens?p=0&size=1000",
            "/api/admin/token",
            "/api/admin/tokens"
        ]

        for endpoint in admin_endpoints:
            try:
                response = await client.get(f"{ONEAPI_BASE_URL}{endpoint}", headers=headers)
                if response.status_code == 200:
                    tokens_info = response.json()
                    if tokens_info.get("success"):
                        tokens_data = tokens_info.get("data", [])

                        # 查找当前API Key对应的token
                        for token in tokens_data:
                            if token.get("key") == api_key:
                                user_id = token.get("user_id")
                                if user_id:
                                    # 尝试获取用户详细信息
                                    user_detail = await get_user_detail_by_id(client, user_id, api_key)
                                    if user_detail:
                                        logger.info(f"✅ 通过管理员权限获取用户信息: {user_detail.get('username', 'unknown')}")
                                        return user_detail

            except Exception as e:
                logger.info(f"管理员端点 {endpoint} 失败: {e}")
                continue

    except Exception as e:
        logger.error(f"管理员权限查询失败: {e}")

    return None

def log_conversation(user_info: Optional[Dict], request_data: Dict, response_data: Dict, duration_ms: int, api_key: str):
    """记录对话到本地文件（仅记录当轮）"""
    try:
        # 仅保留本轮 user 消息
        last_user = _last_user_message(request_data.get("messages", [])) or {"role": "user", "content": ""}

        # 仅保留本轮 assistant 消息（非流式：来自上游；流式：聚合后的）
        assistant_msg = _assistant_message_from_response(response_data)

        # 保证 usage 一定有值（优先用上游；否则估算）
        usage = _ensure_usage(request_data.get("model"), request_data, response_data)

        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "trace_id": str(uuid.uuid4()),
            "user_info": user_info,
            "request": {
                "model": request_data.get("model"),
                # ✅ 只保留当轮这 1 条用户消息
                "messages": [last_user],
                "temperature": request_data.get("temperature"),
                "max_tokens": request_data.get("max_tokens"),
                "stream": bool(request_data.get("stream", False))
            },
            "response": {
                # ✅ 只保留当轮 assistant 的最终文案
                "choices": [{"index": 0, "message": assistant_msg}],
                "usage": usage,
                "model": (response_data or {}).get("model") or request_data.get("model")
            },
            "metadata": {
                "duration_ms": duration_ms,
                "api_key": api_key,
                "api_key_hash": api_key[:8] + "..." if api_key else "unknown"
            }
        }

        # 确保目录存在（双保险）
        os.makedirs(os.path.dirname(LOG_FILE), exist_ok=True)

        with open(LOG_FILE, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
            f.flush(); os.fsync(f.fileno())

        user_name = user_info.get("username", "unknown") if user_info else "unknown"
        logger.info(f"记录对话(当轮) - 用户: {user_name}, 模型: {request_data.get('model')}, tokens: {usage}")
    except Exception as e:
        logger.error(f"记录对话失败: {e}")

@app.get("/", response_class=HTMLResponse)
async def web_interface(request: Request):
    """Web界面 - 需要登录"""
    if not check_login(request):
        return get_login_page()
    return get_web_interface()

@app.get("/login", response_class=HTMLResponse)
async def login_page():
    """登录页面"""
    return get_login_page()

@app.post("/login")
async def login(request: Request, username: str = Form(...), password: str = Form(...)):
    """处理登录"""
    if verify_credentials(username, password):
        request.session["logged_in"] = True
        request.session["username"] = username
        request.session["login_time"] = datetime.now().isoformat()
        logger.info(f"用户 {username} 登录成功")
        return JSONResponse({"message": "登录成功"})
    else:
        logger.warning(f"用户 {username} 登录失败")
        raise HTTPException(status_code=401, detail="用户名或密码错误")

@app.post("/logout")
async def logout(request: Request):
    """登出"""
    username = request.session.get("username", "unknown")
    request.session.clear()
    logger.info(f"用户 {username} 已登出")
    return JSONResponse({"message": "已登出"})

@app.get("/api/config")
async def get_config(request: Request, _: bool = Depends(require_login)):
    """获取当前配置信息（不包含敏感信息）"""
    config = load_login_config()
    safe_config = {
        "username": config.get("username"),
        "created_at": config.get("created_at"),
        "last_updated": config.get("last_updated"),
        "has_custom_config": os.path.exists(CONFIG_FILE)
    }
    return JSONResponse(safe_config)

@app.post("/api/config")
async def update_config(request: Request,
                       new_username: str = Form(...),
                       new_password: str = Form(...),
                       current_password: str = Form(...),
                       _: bool = Depends(require_login)):
    """更新登录配置"""
    # 验证当前密码
    current_username = request.session.get("username")
    if not verify_credentials(current_username, current_password):
        raise HTTPException(status_code=401, detail="当前密码错误")

    # 验证新密码强度
    if len(new_password) < 6:
        raise HTTPException(status_code=400, detail="新密码长度至少6位")

    # 更新配置
    config = load_login_config()
    config["username"] = new_username
    config["password"] = new_password

    if save_login_config(config):
        # 更新全局配置
        global login_config
        login_config = config

        # 更新当前session
        request.session["username"] = new_username

        logger.info(f"用户 {current_username} 更新了登录配置")
        return JSONResponse({"message": "配置更新成功"})
    else:
        raise HTTPException(status_code=500, detail="配置保存失败")

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "oneapi-proxy"}



@app.get("/favicon.ico", include_in_schema=False)
async def favicon():
    # 如需真实图标可改成 FileResponse('static/favicon.ico')
    return Response(content=b"", media_type="image/x-icon")

@app.middleware("http")
async def log_all_requests(request, call_next):
    print(">>>",request.method, request.url.path)
    return await call_next(request)


@app.get("/api/status")
async def proxy_status(request: Request, _: bool = Depends(require_login)):
    """代理状态 - 需要登录"""
    try:
        # 检查OneAPI连接
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get(f"{ONEAPI_BASE_URL}/api/status")
            oneapi_status = response.status_code == 200

        # 检查数据库连接
        database_status = False
        if DATABASE_AVAILABLE:
            try:
                database_status = await test_database_connection()
            except Exception as e:
                logger.error(f"数据库状态检查失败: {e}")

        return {
            "proxy_status": "running",
            "oneapi_connected": oneapi_status,
            "database_connected": database_status,
            "database_available": DATABASE_AVAILABLE,
            "oneapi_url": ONEAPI_BASE_URL,
            "log_file": LOG_FILE,
            "cached_users": len(user_cache),
            "user_resolution_method": "database" if DATABASE_AVAILABLE and database_status else "api_fallback"
        }
    except Exception as e:
        return {"error": str(e)}



@app.get("/__whoami",include_in_schema = False)
async def whoami():
    import os, hashlib
    from datetime import datetime
    p = "/app/proxy-server.py"
    md5 = hashlib.md5(open(p,"rb").read()).hexdigest() if os.path.exists(p) else "NOFILE"
    return {"ts":datetime.utcnow().isoformat()+"Z","file":p,"md5":md5}




@app.post("/v1/chat/completions")
async def chat_completions_proxy(request: Request):
    return await _handle_chat_completions(request)

@app.post("/chat/completions")
async def chat_completions_proxy_alias(request: Request):
    return await _handle_chat_completions(request)

async def _handle_chat_completions(request: Request):
    start = time.time()

    # —— 抓 body（保持你原先的鲁棒解析逻辑即可）——
    raw_body = await request.body()
    try:
        request_data = json.loads(raw_body.decode("utf-8"))
    except Exception:
        # 你的多策略解析（form等）……
        raise HTTPException(status_code=400, detail="Invalid JSON in request body")

    auth_header = request.headers.get("authorization", "")
    api_key = auth_header[7:] if auth_header.startswith("Bearer ") else ""
    user_info = await get_user_info_from_token(api_key)

    # ✅ 如果是流式，请求里强制打开 include_usage（若上游支持就会带 usage）
    stream = bool(request_data.get("stream", False))
    if stream:
        so = request_data.setdefault("stream_options", {})
        so.setdefault("include_usage", True)

    headers = {
        "Authorization": request.headers.get("authorization"),
        "Content-Type": "application/json",
        "User-Agent": request.headers.get("user-agent", "OneAPI-Proxy/1.0")
    }

    if not stream:
        async with httpx.AsyncClient(timeout=60.0) as client:
            upstream = await client.post(f"{ONEAPI_BASE_URL}/v1/chat/completions", json=request_data, headers=headers)
        duration_ms = int((time.time() - start) * 1000)

        ct = upstream.headers.get("content-type", "")
        if upstream.status_code == 200 and ct.startswith("application/json"):
            response_data = upstream.json()
            # ✅ 非流式：直接记录 usage
            log_conversation(user_info, request_data, response_data, duration_ms, api_key)
            return JSONResponse(response_data, status_code=200)
        else:
            # 错误/非JSON：包装为 JSON，避免前端解析失败
            try:
                j = upstream.json()
            except Exception:
                j = {"error": upstream.text, "status_code": upstream.status_code}
            return JSONResponse(j, status_code=upstream.status_code)

    # —— 流式：转发 SSE + 本地聚合，同时尽量抓 usage —— 
    async def streamer():
        collected = []
        usage_from_stream = None
        async with httpx.AsyncClient(timeout=None) as client:
            async with client.stream("POST", f"{ONEAPI_BASE_URL}/v1/chat/completions", json=request_data, headers=headers) as r:
                async for chunk in r.aiter_bytes():
                    # 原样向前端输出
                    yield chunk
                    # 同时尝试解析 usage 与内容
                    try:
                        for line in chunk.decode("utf-8", "ignore").splitlines():
                            if not line.startswith("data: "):
                                continue
                            data = line[6:]
                            if data.strip() == "[DONE]":
                                continue
                            obj = json.loads(data)
                            # 抓增量文本
                            delta = obj.get("choices", [{}])[0].get("delta", {})
                            if "content" in delta:
                                collected.append(delta.get("content") or "")
                            # 抓最终 usage（如果上游支持 include_usage）
                            if "usage" in obj:
                                usage_from_stream = obj["usage"]
                    except Exception:
                        pass

        # 流结束后，拼一个“最终响应”用于日志（只需给日志使用即可）
        try:
            asst_text = "".join(collected)
            response_data = {
                "id": "streamed",
                "object": "chat.completion",
                "model": request_data.get("model"),
                "choices": [{
                    "index": 0,
                    "message": {"role": "assistant", "content": asst_text}
                }]
            }
            if usage_from_stream:
                response_data["usage"] = usage_from_stream

            duration_ms = int((time.time() - start) * 1000)

            ru = _last_user_message(request_data.get("messages",[]))
            print("[debug] current turn user:", (ru or {}).get("content")[:120])

            log_conversation(user_info, request_data, response_data, duration_ms, api_key)
        except Exception as e:
            logger.error(f"stream log failed: {e}")

    return StreamingResponse(streamer(), media_type="text/event-stream")


@app.get("/logs")
async def get_logs(request: Request, limit: int = 10, _: bool = Depends(require_login)):
    """获取最近的对话日志 - 需要登录"""
    print("!! /logs LOG_FILE !!", LOG_FILE)
    try:
        logs = []
        if os.path.exists(LOG_FILE):
            with open(LOG_FILE, "r", encoding="utf-8") as f:
                lines = f.readlines()
                for line in lines[-limit:]:
                    try:
                        logs.append(json.loads(line.strip()))
                    except:
                        continue
        
        return {"logs": logs, "total": len(logs)}
    except Exception as e:
        return {"error": str(e)}

@app.post("/clear-logs")
async def clear_logs(request: Request, _: bool = Depends(require_login)):
    """清空日志 - 需要登录"""
    try:
        if os.path.exists(LOG_FILE):
            os.remove(LOG_FILE)
        return {"message": "日志已清空"}
    except Exception as e:
        return {"error": str(e)}

@app.get("/download-logs")
async def download_logs(request: Request, _: bool = Depends(require_login)):
    """下载日志文件 - 需要登录"""
    try:
        if os.path.exists(LOG_FILE):
            with open(LOG_FILE, "r", encoding="utf-8") as f:
                content = f.read()
            return Response(
                content=content,
                media_type="application/json",
                headers={"Content-Disposition": f"attachment; filename={LOG_FILE}"}
            )
        else:
            return {"error": "日志文件不存在"}
    except Exception as e:
        return {"error": str(e)}


# 代理其他API端点
@app.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def proxy_other_requests(request: Request, path: str):
    """代理其他请求到OneAPI"""
    if path.startswith("v1/chat/completions"):
        return JSONResponse(
            content={"error": "should hit /v1/chat/completions directly"},
            status_code=404  # Method Not Allowed
        )

    if path.startswith(("chat/completions","v1/chat/completions")):
        return JSONResponse({"error": "should hit /v1/chat/completions directly"},status_code=599)
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {k: v for k, v in request.headers.items() if k.lower() != "host"}
            url = f"{ONEAPI_BASE_URL}/{path}"

            if request.method == "GET":
                response = await client.get(url, headers=headers, params=request.query_params)
            else:
                body = await request.body()
                response = await client.request(
                    request.method,
                    url,
                    headers=headers,
                    content=body,
                    params=request.query_params
                )

            # 处理响应
            content_type = response.headers.get("content-type", "")
            logger.info(f"proxy path: {path},响应content-TYPE: {content_type}")
            if content_type.startswith("application/json"):
                try:
                    content = response.json()
                    return JSONResponse(content=content, status_code=response.status_code)
                except:
                    pass

            filtered_headers = {
                k:v for k,v in response.headers.items() if k.lower() not in ("content-length","transfer-encoding","content-encoding")
            }
                            
            return StreamingResponse(
                iter([response.content]),
                status_code=response.status_code,
                headers = filtered_headers,
                media_type=content_type
            )

    except Exception as e:
        logger.error(f"代理请求失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    logger.info(f"启动OneAPI代理服务，端口: {PROXY_PORT}")
    logger.info(f"OneAPI地址: {ONEAPI_BASE_URL}")
    logger.info(f"Web界面: http://localhost:{PROXY_PORT}")
    
    uvicorn.run(app, host="0.0.0.0", port=PROXY_PORT, log_level="info", http="httptools")
