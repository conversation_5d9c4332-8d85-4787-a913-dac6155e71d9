version: '2.1'

services:
  one-api:
    image: oneapi-enhanced:latest
    container_name: one-api
    restart: always
    ports:
      - "3100:3000"
    volumes:
      - ./data/oneapi:/data
      - ./logs:/app/logs
      - /cffex/user/herh/work/oneapi-enhanced-complete-fixed-20250729-142227/tiktoken-cache:/cffex/user/herh/work/oneapi-enhanced-complete-fixed-20250729-142227/tiktoken-cache:ro
    environment:
      - SQL_DSN=oneapi:123456@tcp(db:3306)/one-api
      - REDIS_CONN_STRING=redis://redis
      - TZ=Asia/Shanghai
      - SESSION_SECRET=ae5e609a2057988e420b83e8ff743575dc17cea291d0410d87ef0c06f05add02
      - LANGFUSE_ENABLED=true
      - LANGFUSE_BASE_URL=http://localhost:1919
      - LANGFUSE_PUBLIC_KEY=pk-lf-ac71521b-6a96-4871-85f8-967c7ee634fd
      - LANGFUSE_SECRET_KEY=******************************************
      - LANGFUSE_PROJECT_ID=your-project-id
      - TIKTOKEN_CACHE_DIR=/cffex/user/herh/work/oneapi-enhanced-complete-fixed-20250729-142227/tiktoken-cache
    depends_on:
      - redis
      - db
    healthcheck:
      test: ["CMD-SHELL", "bash -c '</dev/tcp/127.0.0.1/3000' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - oneapi_network

  redis:
    image: redis:7-alpine
    container_name: redis
    restart: always
    networks:
      - oneapi_network
      

  db:
    image: mysql:8.0
    restart: always
    container_name: Oneapi-mysql
    volumes:
      - ./data/mysql:/var/lib/mysql
    ports:
      - '5001:3306'
    environment:
      TZ: Asia/Shanghai
      MYSQL_ROOT_PASSWORD: 'OneAPI@justsong'
      MYSQL_USER: oneapi
      MYSQL_PASSWORD: '123456'
      MYSQL_DATABASE: one-api
    networks:
      - oneapi_network


networks:
  oneapi_network:
    external: 
      name: oneapi_network

  
