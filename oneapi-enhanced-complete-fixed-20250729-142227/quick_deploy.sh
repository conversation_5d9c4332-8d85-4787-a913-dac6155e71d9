#!/bin/bash

# OneAPI代理服务快速部署脚本（基于您的部署命令）
# 适用于已有oneapi_network网络环境

set -e

echo "🚀 OneAPI代理服务快速部署（带登录功能）"
echo "======================================="

# 设置工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 默认配置（基于您的部署命令）
CONTAINER_NAME="logging-system"
HOST_PORT="3101"
CONTAINER_PORT="3001"
NETWORK="oneapi_network"
MYSQL_HOST="Oneapi-mysql"
MYSQL_PORT="3306"
MYSQL_USER="oneapi"
MYSQL_PASSWORD="123456"
MYSQL_DB="one-api"

# 登录配置
ADMIN_USERNAME="${ADMIN_USERNAME:-admin}"
ADMIN_PASSWORD="${ADMIN_PASSWORD:-admin123}"
SESSION_SECRET="${SESSION_SECRET:-oneapi-proxy-session-$(date +%s)}"

echo "📋 部署配置:"
echo "  容器名称: $CONTAINER_NAME"
echo "  服务端口: $HOST_PORT"
echo "  MySQL: $MYSQL_USER@$MYSQL_HOST:$MYSQL_PORT/$MYSQL_DB"
echo "  管理员: $ADMIN_USERNAME"
echo ""

# 询问是否自定义登录凭据
read -p "是否自定义登录凭据? (y/N): " customize
if [[ $customize =~ ^[Yy]$ ]]; then
    read -p "管理员用户名 [$ADMIN_USERNAME]: " input_username
    ADMIN_USERNAME=${input_username:-$ADMIN_USERNAME}
    
    read -s -p "管理员密码 [$ADMIN_PASSWORD]: " input_password
    echo ""
    ADMIN_PASSWORD=${input_password:-$ADMIN_PASSWORD}
fi

# 停止并删除现有容器
echo ""
echo "🛑 清理现有容器..."
docker stop "$CONTAINER_NAME" 2>/dev/null || true
docker rm "$CONTAINER_NAME" 2>/dev/null || true

# 确保必要目录存在
echo ""
echo "📁 准备目录..."
mkdir -p "$(pwd)/logs"
mkdir -p "$(pwd)/config"

# 检查网络是否存在
echo ""
echo "🌐 检查Docker网络..."
if ! docker network ls | grep -q "$NETWORK"; then
    echo "❌ Docker网络 '$NETWORK' 不存在"
    echo "请先创建网络: docker network create $NETWORK"
    echo "或确保OneAPI服务正在运行"
    exit 1
else
    echo "✅ Docker网络 '$NETWORK' 已存在"
fi

# 构建并运行容器（基于您的命令格式）
echo ""
echo "🚀 启动代理服务..."

docker run -d \
  --name "$CONTAINER_NAME" \
  -v "$(pwd)/logging-system:/app" \
  -v "$(pwd)/logging-system/wheels:/wheels" \
  -v "$(pwd)/logs:/app/logs" \
  -v "$(pwd)/config:/app/config" \
  -w /app \
  --network="$NETWORK" \
  -p "$HOST_PORT:$CONTAINER_PORT" \
  -e MYSQL_HOST="$MYSQL_HOST" \
  -e MYSQL_PORT="$MYSQL_PORT" \
  -e MYSQL_USER="$MYSQL_USER" \
  -e MYSQL_PASSWORD="$MYSQL_PASSWORD" \
  -e MYSQL_DB="$MYSQL_DB" \
  -e ADMIN_USERNAME="$ADMIN_USERNAME" \
  -e ADMIN_PASSWORD="$ADMIN_PASSWORD" \
  -e SESSION_SECRET="$SESSION_SECRET" \
  -e CONFIG_FILE="/app/config/login_config.json" \
  -e LOG_DIR="/app/logs" \
  -e ONEAPI_BASE_URL="http://one-api:3000" \
  python:3.11-slim \
  bash -c "apt-get update && apt-get install -y curl && python3 -m pip install --no-index --find-links=/wheels -r ./requirements.txt && python proxy-server.py"

# 等待服务启动
echo ""
echo "⏳ 等待服务启动..."
sleep 20

# 检查容器状态
echo ""
echo "🔍 检查服务状态..."
if docker ps | grep -q "$CONTAINER_NAME"; then
    echo "✅ 容器运行正常"
    
    # 测试服务连接
    max_attempts=10
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://localhost:$HOST_PORT/health" > /dev/null 2>&1; then
            echo "✅ 代理服务启动成功"
            break
        else
            echo "⏳ 等待服务响应... (尝试 $attempt/$max_attempts)"
            sleep 3
            ((attempt++))
        fi
    done
    
    if [ $attempt -gt $max_attempts ]; then
        echo "⚠️  服务可能未完全启动，请检查日志"
        echo "查看日志: docker logs $CONTAINER_NAME"
    fi
else
    echo "❌ 容器启动失败"
    echo "查看日志: docker logs $CONTAINER_NAME"
    exit 1
fi

# 保存快速重启脚本
cat > restart_logging.sh << EOF
#!/bin/bash
# 快速重启脚本
echo "重启OneAPI代理服务..."
docker restart $CONTAINER_NAME
echo "等待服务启动..."
sleep 10
if curl -s http://localhost:$HOST_PORT/health > /dev/null 2>&1; then
    echo "✅ 服务重启成功"
    echo "访问地址: http://localhost:$HOST_PORT"
else
    echo "⚠️  服务可能未完全启动，请检查日志"
    echo "查看日志: docker logs $CONTAINER_NAME"
fi
EOF
chmod +x restart_logging.sh

# 保存停止脚本
cat > stop_logging.sh << EOF
#!/bin/bash
# 停止服务脚本
echo "停止OneAPI代理服务..."
docker stop $CONTAINER_NAME
echo "✅ 服务已停止"
EOF
chmod +x stop_logging.sh

# 显示部署结果
echo ""
echo "🎉 部署完成！"
echo "=================================="
echo "服务信息:"
echo "  访问地址: http://localhost:$HOST_PORT"
echo "  容器名称: $CONTAINER_NAME"
echo "  网络: $NETWORK"
echo ""
echo "登录信息:"
echo "  用户名: $ADMIN_USERNAME"
echo "  密码: [已设置]"
echo ""
echo "文件位置:"
echo "  配置目录: $(pwd)/config/"
echo "  日志目录: $(pwd)/logs/"
echo "  重启脚本: $(pwd)/restart_logging.sh"
echo "  停止脚本: $(pwd)/stop_logging.sh"
echo ""
echo "常用命令:"
echo "  查看日志: docker logs $CONTAINER_NAME"
echo "  重启服务: ./restart_logging.sh"
echo "  停止服务: ./stop_logging.sh"
echo "  进入容器: docker exec -it $CONTAINER_NAME bash"
echo ""
echo "⚠️  重要说明:"
echo "  1. 登录配置已持久化到 config/ 目录"
echo "  2. 可通过Web界面修改登录凭据"
echo "  3. 重启容器后配置和日志保持不变"
echo ""
echo "✅ 请访问 http://localhost:$HOST_PORT 开始使用！"
