#!/usr/bin/env python3
"""
OneAPI代理服务Docker部署登录功能测试脚本
"""

import requests
import json
import sys
import time

def test_docker_deployment():
    """测试Docker部署的登录功能"""
    base_url = "http://localhost:3101"  # 使用您的端口配置
    
    print("🧪 测试OneAPI代理服务Docker部署登录功能...")
    print(f"测试地址: {base_url}")
    
    # 等待服务完全启动
    print("\n⏳ 等待服务启动...")
    max_wait = 30
    for i in range(max_wait):
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ 服务已启动")
                break
        except:
            pass
        
        if i < max_wait - 1:
            print(f"等待中... ({i+1}/{max_wait})")
            time.sleep(2)
    else:
        print("❌ 服务启动超时")
        return False
    
    # 测试1: 检查健康状态
    print("\n1. 测试服务健康状态...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务健康: {data}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False
    
    # 测试2: 访问主页应该返回登录页面
    print("\n2. 测试未登录访问主页...")
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200 and "登录" in response.text:
            print("✅ 未登录用户正确跳转到登录页面")
        else:
            print("❌ 未登录用户没有跳转到登录页面")
            return False
    except Exception as e:
        print(f"❌ 访问主页失败: {e}")
        return False
    
    # 测试3: 测试受保护的API端点
    print("\n3. 测试未登录访问受保护API...")
    try:
        response = requests.get(f"{base_url}/api/status")
        if response.status_code == 401:
            print("✅ 受保护的API正确拒绝未登录访问")
        else:
            print(f"❌ 受保护的API没有正确拒绝访问: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试受保护API失败: {e}")
        return False
    
    # 测试4: 测试错误的登录凭据
    print("\n4. 测试错误的登录凭据...")
    try:
        login_data = {
            "username": "wrong_user",
            "password": "wrong_password"
        }
        response = requests.post(f"{base_url}/login", data=login_data)
        if response.status_code == 401:
            print("✅ 错误凭据正确被拒绝")
        else:
            print(f"❌ 错误凭据没有被正确拒绝: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试错误登录失败: {e}")
        return False
    
    # 测试5: 测试正确的登录凭据
    print("\n5. 测试正确的登录凭据...")
    session = requests.Session()
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = session.post(f"{base_url}/login", data=login_data)
        if response.status_code == 200:
            print("✅ 正确凭据登录成功")
        else:
            print(f"❌ 正确凭据登录失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 测试正确登录失败: {e}")
        return False
    
    # 测试6: 测试登录后访问受保护的API
    print("\n6. 测试登录后访问受保护的API...")
    try:
        response = session.get(f"{base_url}/api/status")
        if response.status_code == 200:
            data = response.json()
            print("✅ 登录后可以访问受保护的API")
            print(f"状态信息: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 登录后无法访问受保护的API，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试受保护API访问失败: {e}")
        return False
    
    # 测试7: 测试配置API
    print("\n7. 测试配置管理API...")
    try:
        response = session.get(f"{base_url}/api/config")
        if response.status_code == 200:
            config = response.json()
            print("✅ 配置API访问成功")
            print(f"配置信息: {json.dumps(config, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 配置API访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试配置API失败: {e}")
        return False
    
    # 测试8: 测试日志API
    print("\n8. 测试日志API...")
    try:
        response = session.get(f"{base_url}/logs?limit=5")
        if response.status_code == 200:
            logs = response.json()
            print("✅ 日志API访问成功")
            print(f"日志数量: {logs.get('total', 0)}")
        else:
            print(f"❌ 日志API访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试日志API失败: {e}")
        return False
    
    # 测试9: 测试登录后访问主页
    print("\n9. 测试登录后访问主页...")
    try:
        response = session.get(f"{base_url}/")
        if response.status_code == 200 and "对话记录" in response.text:
            print("✅ 登录后可以正常访问主页")
        else:
            print("❌ 登录后无法正常访问主页")
            return False
    except Exception as e:
        print(f"❌ 测试登录后主页访问失败: {e}")
        return False
    
    # 测试10: 测试登出功能
    print("\n10. 测试登出功能...")
    try:
        response = session.post(f"{base_url}/logout")
        if response.status_code == 200:
            print("✅ 登出请求成功")
            
            # 验证登出后无法访问受保护的API
            response = session.get(f"{base_url}/api/status")
            if response.status_code == 401:
                print("✅ 登出后无法访问受保护的API")
            else:
                print("❌ 登出后仍然可以访问受保护的API")
                return False
        else:
            print(f"❌ 登出失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试登出功能失败: {e}")
        return False
    
    print("\n🎉 所有Docker部署登录功能测试通过！")
    return True

def test_docker_container():
    """测试Docker容器状态"""
    import subprocess
    
    print("\n🐳 检查Docker容器状态...")
    try:
        # 检查容器是否运行
        result = subprocess.run(
            ["docker", "ps", "--filter", "name=logging-system", "--format", "table {{.Names}}\t{{.Status}}\t{{.Ports}}"],
            capture_output=True, text=True, timeout=10
        )
        
        if result.returncode == 0:
            output = result.stdout.strip()
            if "logging-system" in output:
                print("✅ Docker容器运行正常")
                print(output)
                return True
            else:
                print("❌ logging-system容器未运行")
                return False
        else:
            print(f"❌ Docker命令执行失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Docker命令执行超时")
        return False
    except FileNotFoundError:
        print("⚠️  Docker命令未找到，跳过容器检查")
        return True
    except Exception as e:
        print(f"❌ 检查Docker容器失败: {e}")
        return False

if __name__ == "__main__":
    print("OneAPI代理服务Docker部署登录功能测试")
    print("=" * 60)
    
    # 检查Docker容器状态
    container_ok = test_docker_container()
    
    # 运行登录功能测试
    if test_docker_deployment():
        print("\n✅ 所有测试通过！Docker部署的登录功能工作正常。")
        print("\n📋 测试总结:")
        print("  - 服务健康检查: ✅")
        print("  - 登录页面: ✅")
        print("  - 身份验证: ✅")
        print("  - API保护: ✅")
        print("  - 配置管理: ✅")
        print("  - 日志访问: ✅")
        print("  - 登出功能: ✅")
        if container_ok:
            print("  - Docker容器: ✅")
        
        print(f"\n🌐 访问地址: http://localhost:3101")
        print("🔑 默认登录: admin / admin123")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，请检查配置和实现。")
        print("\n🔧 故障排除建议:")
        print("  1. 检查Docker容器状态: docker ps | grep logging-system")
        print("  2. 查看容器日志: docker logs logging-system")
        print("  3. 检查端口占用: netstat -tlnp | grep 3101")
        print("  4. 验证网络连接: curl http://localhost:3101/health")
        sys.exit(1)
